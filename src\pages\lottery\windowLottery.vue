<template>
  <div class="main">
    <div class="history" v-show="historyShow" :style="{ height: `calc(100% - 10.56rem - ${window ? 0 : 2.875}rem)`, top: `calc(10.56rem + ${window ? 0 : 2.875}rem)` }">
      <div class="wrap">
        <div class="thead">
          <div class="flex1">기수</div>
          <div class="flex2">데이터</div>
        </div>
        <div class="tbody">
          <div class="row" v-for="item in historyList" :key="item.id">
            <div class="flex1">{{ item.expect }}</div>
            <div class="flex2">
              <div class="ballGroup1">
                <van-image class="ball" :src="'img/lottery/shaizi/1' + item.opencode[0] + '.png'"></van-image>
                <van-image class="ball" :src="'img/lottery/shaizi/1' + item.opencode[1] + '.png'"></van-image>
                <van-image class="ball" :src="'img/lottery/shaizi/1' + item.opencode[2] + '.png'"></van-image>
              </div>
              <div class="ballGroup2">
                <span class="ball">{{item?.opencode?.[0] + item?.opencode?.[1] + item?.opencode?.[2]}}</span>
                <span class="ball">{{(item?.opencode?.[0] + item?.opencode?.[1] + item?.opencode?.[2]) >= 11 && (item?.opencode?.[0] + item?.opencode?.[1] + item?.opencode?.[2]) &lt;= 18 ? $t("my.big") : $t("my.small") }}</span>
                <span class="ball">{{(item?.opencode?.[0] + lottery?.opencode?.[1] + lottery?.opencode?.[2]) % 2 === 0 ?  $t("my.double")  :  $t("my.single") }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="header">
      <van-nav-bar v-if="!window">
        <template #left>
          <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
        </template>
        <template #title>
          <span style="font-size: 1rem; color: #fff;">{{ lottery.name }}</span>
        </template>
        <template #right>
          <div class="text_btn" @click="rightShow = true">스위치</div>
        </template>
      </van-nav-bar>
      <div class="top">
        <div class="userInfo">
          <div class="left">
            <img :src="windowDetail.ico || `https://wwkj.luckboos.com/${lottery.ico}`" />
            <span>{{ lottery.next_expect }}</span>
          </div>
          <div class="right">
            <div class="label">남은시간:</div>
            <div class="time"><van-count-down :time="time" @finish="check()" /></div>
          </div>
        </div>
        <div class="gameInfo">
          <div class="left">
            <div class="label">{{ lottery.now_expect }}</div>
            <div class="label">회차</div>
          </div>
          <div class="right">
            <div class="ballGroup">
              <span class="ball">{{lottery?.opencode?.[0] + lottery?.opencode?.[1] + lottery?.opencode?.[2]}}</span>
              <span class="ball">{{(lottery?.opencode?.[0] + lottery?.opencode?.[1] + lottery?.opencode?.[2]) >= 11 && (lottery?.opencode?.[0] + lottery?.opencode?.[1] + lottery?.opencode?.[2]) &lt;= 18 ? $t("my.big") : $t("my.small") }}</span>
              <span class="ball">{{(lottery?.opencode?.[0] + lottery?.opencode?.[1] + lottery?.opencode?.[2]) % 2 === 0 ?  $t("my.double")  :  $t("my.single") }}</span>
            </div>
            <van-icon name="arrow-down" class="icon" @click="historyShow = !historyShow" :class="{historyShow: historyShow}"></van-icon>
          </div>
        </div>
      </div>
      
    </div>

    <div class="content" :style="{ height: `calc(100% - 10.56rem - ${window ? 0 : 2.875}rem)` }">
      <div class="wrap">
        <div class="btn" :class="{ active: values.findIndex(s => s === item.name) >= 0 }" v-for="(item, key) in lottery_peilv_list" :key="key" @click="choosePlay(item.type, item.name);">{{ item.name }}</div>
      </div>
      <div class="bottom" v-show="values.length > 0">
        <div class="row" style="justify-content: space-between;">
          <div style="display: flex;">
            <div class="label">현재 선택: </div>
            <div class="value">{{ String(values) }}</div>
          </div>
          <div class="icon">
            <van-icon name="arrow" color="#ff253f" style="position: relative; right: -0.68rem;"></van-icon>
            <van-icon name="arrow" color="#ff253f"></van-icon>
          </div>
        </div>
        <div class="row">
          <div class="label">수량:</div>
          <input type="number" v-model="params.money" />
        </div>
        <div class="row">
          <div class="label">총</div>
          <div class="num">{{ values.length }}</div>
          <div class="label">티켓 양</div>
          <div class="num">{{ total }}</div>
          <div class="label">완전한</div>
        </div>
      </div>
      <div class="footer">
        <div class="label">사용 가능한 포인트</div>
        <div class="btn" @click="doSub">확인</div>
      </div>
    </div>

    <van-popup v-model="rightShow" position="right">
      <div class="rightWindow">
        <div class="btn" v-for="item in gameList" :key="item.id" @click="chooseGame(item)">{{ item.name }}</div>
      </div>
    </van-popup>
  </div>
</template>
<script>
export default {
  name: 'WindowLottery',
  props: {
    window: {
      type: Boolean,
      default: false
    },
    
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      userInfo: {},
      lottery: {},
      time: 300000,
      shaizi_1: '',
      shaizi_2: '',
      shaizi_3: '',
      sum: '',
      size: '',
      double: '',
      count: 0,
      interval: null,
      historyShow: false,
      historyList: [],
      lottery_peilv_list: [],
      choose: {
        "bi":false,
        "sm":false,
        "si":false,
        "do":false,
        "3":false,
        "4":false,
        "5":false,
        "6":false,
        "7":false,
        "8":false,
        "9":false,
        "10":false,
        "11":false,
        "12":false,
        "13":false,
        "14":false,
        "15":false,
        "16":false,
        "17":false,
        "18":false,
      },
      map: new Map(),
      values: [],
      params: {
        item: '',
        money: 0,
        lid: '',
        mid: '',
        expect: ''
      },
      windowDetail: {
        id: '',
        key: ''
      },
      rightShow: false,
      gameList: []
    };
  },
  computed: {
    total() {
      let total = this.params.money * this.values.length
      if(isNaN(total) || total < 0) {
        return 0
      }
      return total
    },
  },
  watch: {
    detail: {
      async handler(newData) {
        if(newData.id) {
          console.log(newData, 'NewData')
          this.windowDetail.id = newData.id || this.windowDetail.id
          this.windowDetail.key = newData.key || this.windowDetail.key
          this.windowDetail.ico = newData.ico || this.windowDetail.ico
          await this.getUserInfo();
          this.getLotteryInfo();
          this.getLotteryList();
          this.getLotteryPeilv();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    back(){
      return window.history.back();
    },
    check(){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        console.log(1)
        clearInterval(this.interval)
        console.log(2)
        
        this.interval = setInterval(() => {
          console.log(3)
          setTimeout(()=>{
            this.getUserInfo();
            this.getLotteryInfo();
            this.getLotteryList();
            this.count++;
            if(this.count > 2){
              clearInterval(this.interval);
              this.count = 0;
            }
          },0)
        }, 500)
      }
    },
    doSub(){
      if(this.params.money <= 0){
        this.$toast(this.$t("reservation.money_err"));
        return false;
      }
      if(this.values.length == 0){
        this.$toast(this.$t("reservation.choose_num"));
        return false;
      }else if(this.params.money == ""){
        this.$toast(this.$t("reservation.price_place"));
        return false;
      } else {
        if(this.userInfo.money - this.total < 0 ){
          this.$toast(this.$t("reservation.balance_enough"));
          return false;
        }else {
          let data = {
            item:String(this.values.map(item => this.map.get(item))),
            money:this.params.money,
            lid:this.lottery.id,
            mid:this.userInfo.id,
            expect:this.lottery.now_expect
          }
          this.$http({
            method: 'post',
            data: data,
            url: 'game_place_order'
          }).then(res=>{
            if(res.code === 200){
              this.$toast(res.msg);
              this.allClear();
              this.getUserInfo();
              this.getLotteryInfo();
              this.getLotteryList();
              this.getLotteryPeilv();
            }else if(res.code === 401){
              this.$toast(res.msg);
            }
          })
          return true;
        }
      }
    },
    allClear(){
      this.params.money = '';
      this.values = []
    },
    getUserInfo(){
      return this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          this.userInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryPeilv(){
      this.$http({
        method: 'get',
        data:{id:this.windowDetail.id},
        url: 'lottery_get_peilv'
      }).then(res=>{
        if(res.code === 200){
          this.lottery_peilv_list = res.data;
          for(let i in res.data) {
            this.map.set(res.data[i].name, res.data[i].type)
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryList(){
      this.$http({
        method: 'get',
        data:{key:this.windowDetail.key},
        url: 'lottery_get_one_list'
      }).then(res=>{
        if(res.code === 200){
          this.historyList = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryInfo(){
      this.$http({
        method: 'get',
        data:{key:this.windowDetail.key},
        url: 'lottery_get_info'
      }).then(res=>{
        if(res.code === 200){
          if(parseFloat(this.userInfo.money) < parseFloat(res.data.condition)){
            this.$toast(this.$t("reservation.contact_admin"));
            this.$router.push({path:'/Home'})
            return false;
          }
          this.lottery = res.data;
          this.time = res.data.second * 1000;

          if(this.time/1000 === 59){
            this.$toast(this.$t("reservation.prize_succ")+this.lottery.now_expect);
          }
          this.shaizi_1 = "img/lottery/shaizi/" + res.data?.opencode[0] + ".png";
          this.shaizi_2 = "img/lottery/shaizi/" + res.data?.opencode[1] + ".png";
          this.shaizi_3 = "img/lottery/shaizi/" + res.data?.opencode[2] + ".png";
          this.sum = res.data.opencode[0] + res.data.opencode[1] + res.data.opencode[2];
          if(this.sum >= 11 && this.sum <=18){
            this.size = this.$t("my.big");
          }else if(this.sum >= 3 && this.sum <= 10){
            this.size = this.$t("my.small");
          }
          if(this.sum % 2 === 0){
            this.double = this.$t("my.double");
          }else {
            this.double = this.$t("my.single");
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })

    },
    
    choosePlay(type,name){
      const index = this.values.findIndex(item => item === name)
      if(index < 0) {
        this.values.push(name)
      }else {
        this.values = [...this.values.slice(0, index), ...this.values.slice(index + 1, this.values.length)]
      }

    },

    getGameItem(){
      this.$http({
        method: 'get',
        url: 'lottery_list'
      }).then(res=>{
        this.gameList = res.data;
      })
    },

    async chooseGame(e) {
      this.rightShow = false
      this.windowDetail.id = e.id
      this.windowDetail.key = e.key
      this.getUserInfo();
      this.getLotteryInfo();
      this.getLotteryList();
      this.getLotteryPeilv();
      this.getGameItem()
    },
  },
  async created() {
    this.windowDetail.id = this.$route.query.id || this.windowDetail.id
    this.windowDetail.key = this.$route.query.key || this.windowDetail.key
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      if(this.windowDetail.id) {
        await this.getUserInfo();
        this.getLotteryInfo();
        this.getLotteryList();
        this.getLotteryPeilv();
        this.getGameItem()
      }
    }
  },
  destroyed() {
    clearInterval(this.time);
  }
};
</script>

<style lang='less' scoped>
.main{
  background: none;
  overflow: auto;
  height: 100%;
  background: #f0f0f0;
  position: relative;
  background: url(/public/img/lottery/header.png) no-repeat top/100%;
  .history{
    background: rgba(0,0,0,.2);
    position: absolute;
    z-index: 200001;
    width: 100%;
    .wrap{
      height: 22.50rem;
      overflow-y: auto;
      background: #fff;
      position: absolute;
      width: 100%;
      .flex1 {
        width: 33%;
        text-align: center;
      }
      .flex2 {
        width: 67%;
        display: flex;
        justify-content: center;
        margin: 0 1.25rem;
      }
      .thead{
        display: flex;
        height: 2.50rem;
        line-height: 2.50rem;
        >div{
          font-weight: bold;
        }
      }
      .tbody {
        .row{
          height: 2.25rem;
          line-height: 2.25rem;
          display: flex;
        }
        .flex1 {
          font-size: 1.00rem;
          color: #000;
          text-align: center;
        }
        .ballGroup1 {
          width: 50%;
          display: flex;
          align-items: center;
          .ball {
            width: 2.00rem;
            height: 2.00rem;
            margin-right: 0.63rem;
          }
        }
        .ballGroup2 {
          width: 50%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .ball {
            width: 1.88rem;
            height: 1.88rem;
            line-height: 1.88rem;
            text-align: center;
            background: linear-gradient(90deg, #f560cd, #4f2b8a);
            color: #fff;
            border-radius: 6.25rem;
            margin-left: 0.25rem;
          }
        }
      }
    }
  }
  .header{
    .top{
      background: linear-gradient(90deg,rgba(223,53,173,.8),rgba(79,29,181,.8));
      height: 10.56rem;
      padding: 1.13rem 0.75rem;
    }

    .userInfo{
      height: 4.25rem;
      padding-bottom: 0.50rem;
      display: flex;
      justify-content: space-between;
      >div {
        display: flex;
        align-items: center;
        overflow: hidden;
      }
      .left{
        img {
          width: 3.75rem;
          align-self: flex-start;
        }
        span {
          margin-left: 0.75rem;
          color: #fff;
          font-size: 1.13rem;
        }
      }

      .right {
        .label {
          font-size: 0.81rem;
          color: #fff;
        }
        .van-count-down{
          font-size: 0.81rem;
          color: #f4d70a;
          margin-left: 0.63rem;
        }
      }
    }

    .gameInfo {
      height: 4.50rem;
      padding: 0.75rem;
      background: #f47cd5;
      border-radius: 0.38rem;
      display: flex;
      justify-content: space-between;
      .left{
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
      }
      .label{
        font-size: 0.88rem;
        color: #fff;
        width: 100%;
        text-align: center;
      }
      .right {
        width: 14.38rem;
        height: 3.00rem;
        padding: 0 0.75rem;
        background: #fff;
        border-radius: 0.38rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .ballGroup {
        display: flex;
        .ball {
          width: 2.25rem;
          height: 2.25rem;
          background: linear-gradient(90deg,#8b73e6,#d75db4);
          border-radius: 6.25rem;
          font-size: 1.00rem;
          color: #fff;
          line-height: 2.25rem;
          text-align: center;
          margin-right: 1.00rem;
        }
      }
      .icon{
        transform: rotate(0deg);
        transition: .2s all;
      }
      .historyShow{
        transform: rotate(180deg);
      }
    }
    
  }

  .content{
    background: #f0f0f0;
    .wrap{
      padding: 1.38rem 1.38rem 3.75rem;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      position: relative;
      .btn {
        width: 46%;
        height: 5.81rem;
        background: #fff;
        border-radius: 0.38rem;
        margin-bottom: 1.25rem;
        box-shadow: 0 0 0.38rem rgba(0, 0, 0, .1);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ff253f;
        font-size: 1.31rem;
        font-weight: bold;
      }
      .active {
        background: #d07ac7;
        color: #fff;
      }
    }
    .bottom {
      position: absolute;
      background: #fff;
      bottom: 3.56rem;
      width: 100%;
      padding: 0 0.81rem;
      box-shadow: 0 -.13333rem .13333rem rgba(0,0,0,.07);
      .row{
        display: flex;
        padding: 0.38rem 0;
        align-items: center;
        .label {
          font-size: 1.00rem;
          color: #3b3597;
        }
        .value {
          color: #ed37ae;
          margin-left: 0.25rem;
        }
        .icon {
          justify-self: flex-end;
          .van-icon &:first-of-type{
            position: relative;
            right: -0.38rem
          }
        }
        input {
          background: #f2f2f5;
          text-align: center;
          border: 0;
          padding: 0.38rem 0;
          width: 9.38rem;
          margin: 0 0.38rem;
          color: #3b3597;
        }
        .num {
          color: #ed5082;
          padding: 0 0.38rem;
        }
      }
    }
    .footer{
      position: absolute;
      height: 3.56rem;
      background: #fff;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      bottom: 0;
      width: 100%;
      padding: 0 0.81rem;
      .label {
        padding-right: 1.50rem;
        font-size: 1rem;
        color: #302697;
      }
      .btn {
        height: 2.75rem;
        line-height: 2.75rem;
        padding: 0 1.50rem;
        font-size: 1.13rem;
        background: linear-gradient(20deg,#8673e5,#d958a4);
        color: #fff;
        border-radius: 0.4rem;
      }
    }
  }
}


.van-nav-bar{
  background: linear-gradient(90deg,rgba(223,53,173,.8),rgba(79,29,181,.8)) !important;
  position: relative !important;
}
.text_btn{
  font-size: 0.88rem;
  color: #fff;
}
.rightWindow{
  height: calc(var(--vh) * 100);
  width: 60vw;
  overflow: hidden;
  .btn{
    height: 3.00rem;
    border-radius: 0.38rem;
    width: 90%;
    margin: 0.75rem auto;
    font-size: 1.00rem;
    color: #000;
    font-weight: bold;
    text-align: left;
    padding: 0.75rem;
    background: #e4e4e7;
  }
}
</style>
