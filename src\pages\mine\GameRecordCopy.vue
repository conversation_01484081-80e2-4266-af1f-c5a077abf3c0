<template>
  <div class="main">
    <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('my.task_record') }}</span>
      </template>
    </van-nav-bar>
    <div id="content" class="wrapper">
      <div>
        <van-empty image="/img/lottery/nodata.png" v-if="list.length === 0" :description="$t('withdraw.empty_data')" />
        <div v-else>
          <div class="ipu" v-for="(v,key) in list" :key="key">
            <div class="left">
              <img src="/img/lottery/gs.png" class="gs">
              <div class="right">
                <div style="color: #fff;"> {{v.expect}} </div>
                <div class="bai"> {{v.lottery.name}} </div>
                <div class="shuai">
                  <div class="diansd">
                    <van-image class="res-img dianshu " :src=" v.status === 0 ? 'img/lottery/open_num.gif' : 'img/lottery/shaizi/' + v.opencode[0] + '.png' " >
                      <template v-slot:loading>
                        <van-loading type="spinner"/>
                      </template>
                    </van-image>
                    <van-image class="res-img dianshu " :src=" v.status === 0 ? 'img/lottery/open_num.gif' : 'img/lottery/shaizi/' + v.opencode[1] + '.png' " >
                      <template v-slot:loading>
                        <van-loading type="spinner"/>
                      </template>
                    </van-image>
                    <van-image class="res-img dianshu " :src=" v.status === 0 ? 'img/lottery/open_num.gif' : 'img/lottery/shaizi/' + v.opencode[2] + '.png' " >
                      <template v-slot:loading>
                        <van-loading type="spinner"/>
                      </template>
                    </van-image>
                  </div>
                  <div  class="dian">
                    <div  class="shus" style="margin-top: 0.13rem;">{{v.status === 0 ? 0 : v.opencode[0] + v.opencode[1] + v.opencode[2]}} </div>
                    <div  class="shus"> {{v.status === 0 ? 0 : (v.opencode[0] + v.opencode[1] + v.opencode[2]) >= 11 && (v.opencode[0] + v.opencode[1] + v.opencode[2]) &lt;= 18 ? $t("my.big") : $t("my.small") }} </div>
                    <div  class="shus"> {{v.status === 0 ? 0 : (v.opencode[0] + v.opencode[1] + v.opencode[2]) % 2 === 0 ?  $t("my.double")  :  $t("my.single") }} </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="background: rgb(255, 255, 255); opacity: 0.1; width: 100%; height: 0.13rem; margin-top: 4%; margin-bottom: 4%;"></div>
            <div class="left left2 li_head">

              <div class="li">
                <div class="name">{{$t("reservation.order_time")}} </div>
                <div class="tt"> {{v.create_time}} </div>
              </div>
              <div class="li">
                <div class="name"> {{$t("reservation.settle_time")}} </div>
                <div class="tt"> {{v.update_time}} </div>
              </div>
              <div class="li">
                <div class="name"> {{$t("reservation.type")}} </div>
                <div class="tt">{{v.type_name}}</div>
              </div>
              <div class="li">
                <div class="name"> {{$t("reservation.money")}} </div>
                <div class="tt" style="color: rgb(255, 26, 109);"> {{v.money}} </div>
              </div>
            </div>
            <!-- <div class="left " v-for="(item, index) in v.data" :key="index">
              <div class="li">
                <div class="tt"> {{item.create_time}} </div>
              </div>
              <div class="li">
                <div class="tt"> {{item.update_time}} </div>
              </div>
              <div class="li">
                <div class="tt">{{item.type_name}}</div>
              </div>
              <div class="li">
                <div class="tt" style="color: rgb(255, 26, 109);"> {{item.money}} </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isLoading: false,
      list:[],
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    onRefresh() {
      setTimeout(() => {
        this.$toast('새로고침 성공');
        this.isLoading = false;
      }, 500);
    },
    getUserGameList(){
      this.$http({
        method: 'get',
        url: 'user_get_game_list'
      }).then(res=>{
        console.log(res, 'game------------------')
        let list = []
        if(res.code === 200){
          res.data.map(item => {
            list = [...list, ...item]
          })
          this.list = res.data.map(item => {
            return item
          })
          this.list = list
          console.log(list)
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    }

  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserGameList();
    }
  }
}
</script>

<style lang='less' scoped>

#content{
  padding-bottom: 3.2rem;
}
.main{
  background: none;
}

/deep/ .van-empty__description {
  font-size: 1.1rem;
}

/deep/ .van-empty__image{
  width: 10.75rem;
  height: 10.75rem;
}
.ipu {
    width: 92%;
    margin: 0 auto;
    background: linear-gradient(264deg, #7C59A5 0%, #EE7DD4 100%);;
    border-radius: 0.50rem;
    margin-top: 5%;
    padding: 4%
}

.left {
    display: flex;
    align-items: center;
}
.gs {
    width: 6.875rem;
    height: 6.25rem;
}

.right {
  margin-left: 5%;
  width: 60%;
}
.red {
    color: #ff1a6d;
    font-size: 1rem;
    font-weight: 500;
}
.bai {
    color: #fff;
    font-size: 1rem;
    margin-top: 5%;
    margin-bottom: 5%;
}
.shuai {
    display: flex;
    margin-top: 8%;
}
.diansd {
    display: flex;
    align-items: center;
    width: 60%;
}
.dianshu {
    margin-right: .625rem;
    width: 1.5rem;
    height: 1.5rem;
}
.dian {
    display: flex;
    align-items: center;
    color: #fff;
    margin-left: 8%;
    width: 40%;
}
.shus {
    padding-right: 11%;
}



.left2 {
    margin-top: 5%;
    display: flex;
    flex-direction: row;
    margin-left: 0;
    align-items: flex-start;
}
.li {
    width: 25%;
}
.name {
    color: #fff;
    font-size: .8rem;
    text-align: center;
}
.tt {
    color: #fff;
    margin-top: 15%;
    font-size: .9rem;
    text-align: center;
}

</style>
