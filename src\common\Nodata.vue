<template>
  <div class="NoData">
    <div class="imgWrap">
      <img src="/img/empty.png" />
    </div>
    <div class="label">{{ $t('common.nodata') }}</div>
  </div>
</template>
<script lang="ts" setup>
</script>
<style lang="less" scoped>
.NoData {
  height: 11.25rem;
  margin: 6.25rem 0 0;
  padding: 2.00rem 0;
  .imgWrap {
    display: flex;
    justify-content: center;
    img {
      width: 5.00rem;
    }
  }
  .label {
    width: 100%;
    text-align: center;
    margin-top: 1rem;
    font-size: 0.88rem;
    color: #969799;
  }
}
</style>
