<template>
  <div class="container">
    <div class="header" id="content">
      <van-nav-bar :title="$t('MyMove.title')">
        <template #left>
          <van-icon name="arrow-left" color="#fff" size="20"  @click="back()"></van-icon>
        </template>
      </van-nav-bar>
      <div class="info">
        <div class="avatar">
          <van-image round class="user_img" :src="userInfo.header_img">
            <template v-slot:error>
              <van-image src="img/nan/11.png"></van-image>
            </template>
          </van-image>
        </div>
        <div class="div">
          <div class="title">Hi,{{ userInfo.username }}</div>
          <div class="label">{{ $t('MyMove.info') }}</div>
        </div>
        <div class="btn">{{ $t('MyMove.btn') }}:0</div>
      </div>
    </div>

    <div class="content">
      <div class="row">
        <img src="img/mine/MyMove.png" alt="">
        <div class="label">{{ $t('MyMove.label') }}</div>
      </div>
      <Nodata />
    </div>
  </div>
</template>

<script>
import Nodata from '@/common/Nodata.vue';
export default {
  components: {Nodata},
  data() {
    return {
      userInfo: {},
      recharge_balance: null
    };
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
    }
    
  },
  methods: {
    back(){
      return window.history.back();
    },
    goRecord() {
      this.$router.push("ChongRecord");
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
            this.userInfo = res.data;
            if(this.userInfo.status !== 1){
              this.$toast(this.$t("video.account_out"));
              localStorage.clear()
              this.$router.push({path:'/Login'})
            }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    submit() {
      if(!this.recharge_balance) {
        return
      }
      this.$http({
        url: "userRecharge",
        method: "post",
        data: { 
          amount: this.recharge_balance,
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$toast.success(res.msg);
          // localStorage.setItem("token", res.data.id);
        } else {
          this.$toast(res.msg);
        }
      });
    },
    toKefu() {
      window.open(this.$store.getters.getBaseInfo.kefu)
    }
  }
};
</script>

<style lang='less' scoped>
.container{
  // background: url("/public/img/user/withdrawBg.png") no-repeat top / 100%, #fff;
}

.van-nav-bar {
  /deep/ .van-nav-bar__title{
    color: #fff;
    font-size: 1rem;
  }
}

.header{
  // height: 10.00rem;
  background: #f0f0f0;
  .info{
    height: 6.2rem;
    margin: 1.13rem 0.69rem;
    background: #fff;
    display: flex;
    padding: 0 0.81rem;
    align-items: center;
    justify-content: space-between;
    .avatar{
      border: 0.25rem solid #d4d0e6;
      background: #f7f8fa;
      border-radius: 6.25rem;
      height: 3.75rem;
      width: 3.75rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 1.13rem;
      .van-image {
        width: 3.25rem;
        height: 3.25rem;
      }
    }
    .title{
      color: #48328b;
      font-size: 1.06rem;
      font-weight: bold;
      margin-bottom: 0.44rem;
    }
    .label{
      font-size: 0.88rem;
      color: #523e90;
      height: 2.00rem;
    }
    .btn{
      min-width: 5.88rem;
      height: 2.19rem;
      line-height: 2.19rem;
      background: linear-gradient(30deg,#8073eb,#e055a0);
      font-size: 0.88rem;
      color: #fff;
      text-align: center;
      border-radius: 2.50rem;
    }
  }
}

.content{
  height: calc(var(--vh) * 100  - 11.34rem);
  background: #fff;
  padding: 1.38rem;
  .row{
    display: flex;
    img{
      width: 1.50rem;
      height: 1.44rem;
      margin-right: 0.38rem;
    }
    .label{
      font-size: 1rem;
      color: #4a338b;
      font-weight: bold;
    }
  }
}

</style>
