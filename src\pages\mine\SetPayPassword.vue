<template>
  <div class="container">
    <van-nav-bar title="로그인 비밀번호 변경">
      <template #left>
        <van-icon name="arrow-left" color="#412188" size="20"  @click="back()"></van-icon>
      </template>
    </van-nav-bar>
    <div class="card">
      <div class="row" v-if="userInfo.paypassword">
        <div class="title">{{ $t('setting.old_pwd') }}</div>
        <div class="input_wrap">
          <van-field v-model="old_password" :placeholder="$t('setting.old_pwd_tip')" :type="pwd1Type == 1 ? 'password' : 'text'">
            <template #right-icon>
              <van-icon :name="pwd1Type == 1 ? 'eye-o' : 'closed-eye'" color="#f53d81" size="24" @click="pwd1Type = pwd1Type == 1 ? 2 : 1"></van-icon>
            </template>
          </van-field>
        </div>
      </div>
      <div class="row">
        <div class="title">{{ $t('setting.new_pwd') }}</div>
        <div class="input_wrap">
          <van-field v-model="o_new_password" :placeholder="$t('setting.new_pwd_tip')" :type="pwd2Type == 1 ? 'password' : 'text'">
            <template #right-icon>
              <van-icon :name="pwd2Type == 1 ? 'eye-o' : 'closed-eye'" color="#f53d81" size="24" @click="pwd2Type = pwd2Type == 1 ? 2 : 1"></van-icon>
            </template>
          </van-field>
        </div>
      </div>
      <div class="row">
        <div class="title">{{ $t('setting.new_again') }}</div>
        <div class="input_wrap">
          <van-field v-model="t_new_password" :placeholder="$t('setting.new_again_tip')" :type="pwd3Type == 1 ? 'password' : 'text'">
            <template #right-icon>
              <van-icon :name="pwd3Type == 1 ? 'eye-o' : 'closed-eye'" color="#f53d81" size="24" @click="pwd3Type = pwd3Type == 1 ? 2 : 1"></van-icon>
            </template>
          </van-field>
        </div>
      </div>
    </div>
    <div class="btn" @click="save()">{{$t("setting.button")}}</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      pwd1Type: 1,
      pwd2Type: 1,
      pwd3Type: 1,
      o_new_password:"",
      t_new_password:"",
      old_password:"",
      userInfo:{}
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    save(){
      if(this.o_new_password === "" || this.o_new_password === null || this.o_new_password === undefined){
        this.$toast.fail(this.$t("setting.prefect"));
        return false;
      }
      if(this.t_new_password === "" || this.t_new_password === null || this.t_new_password === undefined){
        this.$toast.fail(this.$t("setting.prefect"));
        return false;
      }
      if(this.old_password === "" || this.old_password === null || this.old_password === undefined){
        this.$toast.fail(this.$t("setting.prefect"));
        return false;
      }
      if(this.o_new_password !== this.t_new_password){
        this.$toast(this.$t("setting.pwd_error"));
        return false;
      }
      this.$http({
        method: 'post',
        data:{
          old_pay_password: this.old_password,
          new_pay_password: this.o_new_password
        },
        url: 'user_set_paypwd'
      }).then(res=>{
        if(res.code === 200){
          this.$toast(res.msg);
          setTimeout(() => {
            this.back()
          }, 500);

        }else {
          this.$toast(res.msg);
        }
      })
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          this.userInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    }
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
    }
  }
};
</script>

<style lang='less' scoped>
.container{
  background: linear-gradient(180deg, #fff, #fefae9);
}

.van-nav-bar {
  background: none;
  height: 2.88rem;
  position: relative !important;

  /deep/ .van-nav-bar__title{
    color: #412188;
    font-size: 1rem;
  }
  /deep/ .van-nav-bar__left{
    padding: 0 1rem;
  }
}



.van-cell {
  font-size: 2.19rem;
  line-height: 5.00rem;
  background-color: #22063a;
}
/deep/ .van-field__control {
  color: #323233!important;
}
.card{
  padding: 1.00rem 1.25rem;
  .title{
    color: #49328b;
    font-size: 0.88rem;
  }
  .input_wrap{
    background-color: inherit !important;
    background: #321b51;
    border-radius: 6.25rem;
    border: 0.13rem solid #ccc4cf;
    height: 3.00rem;
    margin-top: 4.5%;
    margin-bottom: 4.5%;
    color: #323233;
    display: flex;
    align-items: center;
  }
  
  .van-field{
    background-color: transparent;
    padding: 0 1.25rem;

    color: #fff;
    &::-webkit-input-placeholder {
      color: #323233;
    }
    font-size: 0.88rem;
    display: flex;
    align-items: center;
    border-bottom: 0 !important;
    border: none;
    height: 1.50rem;
    /deep/ .van-field__control{
      color: #323233;
      background-color: transparent;
    }
    /deep/ .van-field__value{
      width: 100%;
      height: 100%;
      line-height: 2rem;
    }


    
    /deep/ .van-field__body{
      height: 1.50rem;
    }
    
  }
}
.btn{
  display: inline-block;
  width: 90%;
  margin: 0 auto 2rem;
  background: linear-gradient(20deg, #9d50cf, #ee5380);
  color: #fff;
  padding: .86667rem 0;
  border-radius: 3rem;
  font-size: .93333rem;
  cursor: pointer;
  text-align: center;
}
</style>
