<template>
  <div class="movie-hall page">
    <div class="head">{{ $t('video.video') }}</div>
    <div id="content">
      <van-tabs v-model="active" animated swipeable  @change="OnChange">
        <van-tab v-for="(v,key) in videolitem" :key="key" :title="v.name" :name="v.key" ></van-tab>
      </van-tabs>
      <div class="movie-list-tab">
        <van-pull-refresh v-model="isLoading" @refresh="refresher" class="container" :pulling-text="$t('common.pullLabel1')" :loosing-text="$t('common.pullLabel2')" :loading-text="$t('common.pullLabel3')">
          <Nodata v-if="videolist.length === 0" />
          <van-list v-model="loading" :finished="finished" :finished-text="$t('common.nomore')" :loading-text="$t('common.loading')" :error-text="$t('common.error')" @load="onLoad">
            <div class="wrap">
              <div class="video_wrap" v-for="(v,key) in videolist" :key="key" @click="toPlayVideo(v)">
                <div class="video">
                  <van-image  :src="v.vod_pic"></van-image>
                  <div class="info">
                    <span class="label">{{v.vod_name}}</span>
                    <span class="value">{{$t("video.play")}}:{{v.count}}</span>
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';
import Nodata from "@/common/Nodata.vue";
export default {
  components: { Nodata },
  data() {
    return {
      active: 0,
      isLoading: false,
      count:0,
      loading: false,
      finished: false,
      refreshing: false,
      videolitem: [],
      videolist: [],
      number:0,
      page:0,
      videoSwiperOption: {
        slidesPerView: 'auto',
        spaceBetween: 0,
        slidesPerGroup : 1,
      }
    };
  },
  computed: {
    type() {
      return this.videolitem.filter((item, index) => index === this.active)?.[0]?.id || 1
    }
  },
  methods: {
    getVideoClass(){
      return this.$http({
        method: 'get',
        url: 'video_class'
      }).then(res=>{
        this.videolitem = res.data;
      })
    },
    toPlayVideo(v){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        this.$router.push({path:`/PlayVideo?id=${v.id}&title=${v.vod_name}`})
      }

    },
    async getVideoList(refresh=true){
      if(this.videolitem.length === 0) {
        await this.getVideoClass();//获取视频类目
      }
      return this.$http({
        method: 'get',
        data:{id:this.type,page:this.page},
        url: 'video_list'
      }).then(res=>{
        this.count = res.data.count;
        if(refresh) {
          this.videolist = res.data.data
        }else {
          this.videolist = [...this.videolist, ...res.data.data]
        }
        this.loading = false
        if(this.videolist.length >= this.count) {
          this.finished = true
        }
      })
    },
    onLoad() {
      console.log('onload')
      this.page ++
      this.getVideoList(false);
    },
    OnChange(){
      this.videolist = [];
      this.number = 0;
      this.page = 0;
      this.count = 0;
      this.finished = false
      this.loading = true
      this.getVideoList();//获取视频列表
    },

    async refresher() {
      this.isLoading = true
      this.page = 1
      await this.getVideoList()
      this.finished = false
      this.isLoading = false
    },
  },
  created() {
    // this.getVideoClass();//获取视频类目
    // this.OnChange()
  }
};
</script>

<style lang='less' scoped>
.page{
  background: url('/public/img/bg.png') repeat top / 100% auto;
  .head {
    display: flex;
    justify-content: center;
    height: 2.88rem;
    font-weight: bold;
    align-items: center;
  }
  #content {
    padding-top: 0;
  }
}

::v-deep .van-tabs__nav {
  background: transparent;
  gap: 0.63rem;
  align-items: center;
}
::v-deep .van-tab {
  color: #442889;
  background: #fff;
  border-radius: 5.63rem;
  height: 1.75rem;
  font-weight: 400;
  font-size: 0.88rem;
}
::v-deep .van-tabs--line .van-tabs__wrap {
  height: 2.75rem;
}
::v-deep .van-tabs__wrap--scrollable .van-tab {
  padding: 0 1rem;
}
::v-deep  .van-hairline--bottom::after {
  border-bottom-width: 0.00rem;
}
::v-deep .van-tabs__line {
  display: none;
}
::v-deep  .van-tab--active {
  font-weight: bold;
  font-size: 1.06rem;
  background: linear-gradient(180deg, #fc78af, #f22678);
  color: #fff;
  box-shadow: 0 2px 6px rgba(242, 38, 120, .25);
}

.video_swiper {
  width: 100%;
  flex: 1;
  .swiper-slide {
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: 100%;
    justify-content: center;
    height: 100%;
    position: relative;
    transition-property: transform;
  }
}
.movie-list-tab {
  overflow: auto;
  height: 100%;
}
::v-deep .van-pull-refresh__track .van-pull-refresh__head *{
  color: #000;
  font-size: 2.19rem;
}
.movie-list-tab .hot-recommend-div{
  height: 100%;
  margin: 0.63rem auto;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  //overflow: auto;
}



.container {
  height: calc(var(--vh) * 100 - 2.88rem - 2.75rem - 3.75rem);
  overflow-y: auto;
}
.wrap{
  display: flex;
  flex-wrap: wrap;
  padding: 0 0.3rem;
  .video_wrap {
    height: 7.50rem;
    width: 50%;
    margin-top: 0.75rem;
  }
  .video {
    margin: 0 0.3rem;
    height: 7.50rem;
    position: relative;
    border-radius: 0.33rem;
    overflow: hidden;
    .van-image{
      width: 100%;
      height: 100%;
    }
    .info {
      height: 1.88rem;
      width: 100%;
      position: absolute;
      background: #00000066;
      padding: 0.38rem 0;
      z-index: 10000;
      bottom: 0;
      display: flex;
      .label, .value {
        width: 50%;
        padding: 0 0.38rem;
        font-size: 0.88rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #fff;
        text-align: center;
      }
      
    }
  }
}

::v-deep .van-list__finished-text{
  font-size: 1.00rem !important;
  height: 3.13rem;
  line-height: 3.13rem;
}
::v-deep .van-loading__text {
  font-size: 1.00rem !important;
}
</style>
