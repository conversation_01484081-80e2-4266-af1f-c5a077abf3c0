
import { Toast } from "vant";
export default{
    isLogin:function(_this){
        if(!_this.$store.getters.getLoginValue){
            return _this.$router.push("Login")
        }
    }
}



// 时间戳转换
export const timestempToDate = (timestamp, n = '-', type = 1) => {
    const date = new Date(timestamp)
    const year = date.getUTCFullYear()
    const month = date.getUTCMonth() + 1 < 10 ? `0${date.getUTCMonth() + 1}` : date.getUTCMonth() + 1
    const day = date.getUTCDate() < 10 ? `0${date.getUTCDate()}` : date.getUTCDate()
    const hour = date.getUTCHours() < 10 ? `0${date.getUTCHours()}` : date.getUTCHours()
    const minute = date.getUTCMinutes() < 10 ? `0${date.getUTCMinutes()}` : date.getUTCMinutes()
    const seconds = date.getUTCSeconds() < 10 ? `0${date.getUTCSeconds()}` : date.getUTCSeconds()
    if(type === 1) {
        return `${year}${n}${month}${n}${day} ${hour}:${minute}:${seconds}`
    }else if(type === 2) {
        return `${day}${n}${month}${n}${year}`
    }else if(type === 3) {
        return `${hour}:${minute}:${seconds}`
    }
}

export const changeMoney = (money) => {
    if (isNaN(Number(money))) {
      return '-'
    }
    money = Number(money).toFixed(0)
    const unit1 =  ','
    const x = money.split('.')
    let x1 = x[0]
    const reg = /(\d+)(\d{3})/
    while (reg.test(x1)) {
      x1 = x1.replace(reg, '$1' + unit1 + '$2')
    }
    return `${x1}`
}



// 多个函数同时加载的loading
export const waitWithLoading = async (tasks) => {
//   showLoadingToast({ forbidClick: true, duration: 0 })
    Toast.loading({forbidClick: true})
  const promises = tasks.map((fn) => fn().catch(() => {})) // 忽略个别错误，保证全部执行

  try {
    await Promise.all(promises)
  } finally {
    closeToast()
  }
}