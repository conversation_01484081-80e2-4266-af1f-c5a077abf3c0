<template>
  <van-tabbar
    v-if="show"
    v-model="active"
    active-color="#ff1a6d"
    :border="true"
    inactive-color="#8f80ad"
  >
  <!-- 首页 -->
    <van-tabbar-item to="/Home">
      <span>{{ $t("tabbar.index") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/icon1_select.png' : '/img/footer/icon1.png'"
          :alt="$t('tabbar.index')"
        />
      </template>
    </van-tabbar-item>
    <!-- 视频 -->
    <van-tabbar-item to="/Video">
      <span>{{ $t("tabbar.video") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/icon2_select.png' : '/img/footer/icon2.png'"
          :alt="$t('tabbar.video')"
        />
      </template>
    </van-tabbar-item>
    <!-- 中间 -->
    <van-tabbar-item to="/Goods">
      <span>{{ $t("tabbar.goods") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/icon3_select.png' : '/img/footer/icon3.png'"
        />
      </template>
    </van-tabbar-item>
    <!-- 视频 -->
    <van-tabbar-item to="/Lottery">
      <span>{{ $t("tabbar.message") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/icon4_select.png' : '/img/footer/icon4.png'"
          :alt="$t('tabbar.message')"
        />
      </template>
    </van-tabbar-item>
    <!-- 我的 -->
    <van-tabbar-item to="/Mine">
      <span>{{ $t("tabbar.user") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/icon5_select.png' : '/img/footer/icon5.png'"
          :alt="$t('tabbar.user')"
        />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      active: 0
    };
  },
  methods: {},
  watch: {
    $route(to) {
      console.log(to)
      if (to.name == "home") {
        this.active = 0;
        this.show = true;
      } else if (to.name == "video") {
        this.active = 1;
        this.show = true;
      } else if (to.name == "Goods") {
        this.active = 2;
        this.show = true;
      } else if (to.name == "Lottery") {
        this.active = 3;
        this.show = true;
      } else if (to.name == "mine") {
        this.active = 4;
        this.show = true;
      } else {
        this.show = false;
      }
    },
  },
  created() {
    if (this.$route.name == "home") {
      this.active = 0;
      this.show = true;
    } else if (this.$route.name == "video") {
      this.active = 1;
      this.show = true;
    } else if (this.$route.name == "Goods") {
      this.active = 2;
      this.show = true;
    } else if (this.$route.name == "Lottery") {
      this.active = 3;
      this.show = true;
    } else if (this.$route.name == "mine") {
      this.active = 4;
      this.show = true;
    } else {
      this.show = false;
    }
  },
};
</script>

<style lang="less" scoped>
@tabbar-height: 3.75rem;
@tabbar-img: 1.38rem;
.van-tabbar {
  width: 31.25rem;
  height: @tabbar-height;
  background: #fafafa;
  box-shadow: 0 0 0.63rem rgba(0, 0, 0, .1);
  left: calc(50vw - 15.625rem);
}
.van-tabbar-item__icon img {
  height: @tabbar-img;
}
.van-tabbar-item {
  font-size: 0.75rem;
  background: #fafafa;
}

[class*="van-hairline"]::after {
  border: none !important;
}


@media (max-width: 31.25rem) {
  .van-tabbar {
    width: 100%;
    left: calc(50vw - 50%);
  }
}
</style>
