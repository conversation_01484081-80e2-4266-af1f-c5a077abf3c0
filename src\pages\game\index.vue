<template>
<div>
  <van-nav-bar>
    <template #left>
      <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
    </template>
    <template #title>
      <span style="font-size: 1rem; color: #fff;">{{ $t('reservation.hall') }}</span>
    </template>
  </van-nav-bar>
  <div id="content">
    <div class="rows">
      <div @click="toLottery(val.key,val.id)" v-for="(val, key) in gameitem" :key=key class="bg">
        <div>
          <div class="game_item_img gs van-image">
            <img :src="val.ico" class="van-image__img">
          </div>
        </div>
        <div class="rg">
          <div class="class1">{{val.name}}</div>
        </div>
      </div>
    </div>
    <van-overlay :show="isLoading">
      <van-loading type="spinner" style="top:50%;left:50%;" />
    </van-overlay>

  </div>
</div>
</template>

<script>
import { Toast } from 'vant';
export default {
  data() {
    return {
      gameitem: [],
      lotteryitem: [],
      isLoading: false,
      activeKey: 0,
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    onRefresh() {
      setTimeout(() => {
        Toast(this.$t("reservation.refresh"));
        this.isLoading = false;
      }, 500);
    },
    toLottery(key,id){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        this.$router.push({path:'/Lottery?key='+key+"&id="+id})
      }
    },
    getGameItem(){
      this.$http({
        method: 'get',
        url: 'lottery_list'
      }).then(res=>{
        console.log(res.data)
        this.gameitem = res.data;
      })
    },
    onChange(index) {
      this.isLoading = true
      this.$http({
        method: 'get',
        data:{class:index},
        url: 'lottery_list'
      }).then(res=>{
        this.gameitem = res.data
        this.isLoading = false
      })
    },
    getLotteryItem(){
      this.$http({
        method: 'get',
        url: 'lottery_class'
      }).then(res=>{
        this.lotteryitem = res.data;
      })
    }
  },
  created() {
    this.getGameItem();//获取首页游戏列表
    this.getLotteryItem();
  }
};
</script>

<style lang='less' scoped>

#content{
  padding-bottom: 3.2rem;
}
.main{
  background: none;
}
.shijian {
    width: 80%;
    margin: 0.5rem auto;
    color: #fff;
    font-size: 1.1rem;
    margin-top: 5%
}
/deep/ .van-tab{
  color: #fff;
  font-size: 1.1rem;
  line-height: 2rem;
}
/deep/ .van-tabs__nav {
  background: #fff0;
}

/deep/.van-tabs__line {
    position: absolute;
    bottom: 0.94rem;
    left: 0;
    z-index: 1;
    min-width: 3.13rem;
    height: 2.75rem;
    padding: 0 0.63rem;
    background-color: #dddddd00;
    border-radius: 1.88rem;
    box-sizing: content-box;
}
/deep/.van-tab--active{
    font-weight: bold;
    color: #6daf4a
}
.rows {
    position: relative;
    height: 100%;
}
.bg {
  background: #DC7DD0;
  border-radius: .8rem;
  width: 90%;
  margin: 0 auto;
  margin-top: 5%;
  display: flex;
  padding: 5%
}

.gs {
    position: relative;
    height: 5rem;
    width: 5rem;
    margin-right: 5%;
    display: flex;
    align-items: center;
}
.rg {
    margin-left: 5%;
    display: flex;
    align-items: center;
}
.class1 {
    color: #fff;
    font-size: 1.1375rem
}
</style>
