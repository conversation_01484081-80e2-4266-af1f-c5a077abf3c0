<template>
  <div class="main">
    <div class="head">{{ $t('goods.title') }}</div>
    <div id="content">
      
      <van-tabs v-model="active" animated swipeable  @change="OnChange" v-show="allAddressList.length > 1">
        <van-tab v-for="(v,key) in allAddressList" :key="key" :title="v.name" :name="key" ></van-tab>
      </van-tabs>

      <div class="view">
        <div class="left">
          <div class="card" v-for="item in xuanfeiLeftList" :key="item.id" @click="goDetail(item)">
            <div class="img_wrap">
              <img :src="item.img_url[0]">
              <div class="like"></div>
              <div class="tag">{{  $t('goods.tag') }}</div>
            </div>
            <div class="row">
              <div class="left">
                <img :src="item.img_url[0]">
                <span>{{ item.xuanfei_name }}</span>
              </div>
              <div class="right">
                <img src="/img/goods/location.png" alt="">
                <span>{{ item.diqu }}</span>
              </div>
            </div>
            <div class="biaoqian">
              <div v-for="(biaoqian, index) in item.type.slice(0, 1)" :key="index">{{ biaoqian.name }}</div>
            </div>
            <div class="xingji">
              <div>
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
              </div>
            </div>
            <div class="button">{{ $t('index.button') }}</div>
          </div>
        </div>
        <div class="right">
          <div class="card" v-for="item in xuanfeiRightList" :key="item.id" @click="goDetail(item)">
            <div class="img_wrap">
              <img :src="item.img_url[0]">
              <div class="like"></div>
              <div class="tag">{{  $t('goods.tag') }}</div>
            </div>
            <div class="row">
              <div class="left">
                <img :src="item.img_url[0]">
                <span>{{ item.xuanfei_name }}</span>
              </div>
              <div class="right">
                <img src="/img/goods/location.png" alt="">
                <span>{{ item.diqu }}</span>
              </div>
            </div>
            <div class="biaoqian">
              <div v-for="(biaoqian, index) in item.type.slice(0, 1)" :key="index">{{ biaoqian.name }}</div>
            </div>
            <div class="xingji">
              <div>
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
                <van-icon name="star" color="#f4b757" size="1rem" />
              </div>
            </div>
            <div class="button">{{ $t('index.button') }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      addressList: [],
      addressActive: 0,
      age: null,
      search: {
        class_id: 0,
        page: 1,
        limit: 999,
        name: '',
        type: 1
      },
      allXuanfeiList: [],
      ageList: [],
      active: 0,
    };
  },
  watch: {
    addressActive(newVal) {
      if(newVal === 0) {
        this.search.class_id = 0
      }else {
        this.search.class_id = this.addressList[newVal - 1].id
      }
      console.log(this.search.class_id)
      this.getXuanfeiList()
    }
  },
  created() {
    this.getAddress();
    this.getXuanfeiList();
  },
  computed: {
    allAddressList() {
      return [{key: 0, name: this.$t('goods.every')}].concat(this.addressList.map(item => ({key: item.id, name: item.name})))
    },
    xuanfeiList() {
      return this.allXuanfeiList.filter(item => {
        if (this.age) {
          return item.age === this.age
        } else {
          return true
        }
      })
    },
    xuanfeiLeftList() {
      return this.xuanfeiList.filter((item, index) => {
        return index % 2 == 0
      })
    },
    xuanfeiRightList() {
      return this.xuanfeiList.filter((item, index) => {
        return index % 2 !== 0
      })
    }
  },
  methods: {
    OnChange(e) {
      this.addressActive = e
      console.log(e)
    },
    showTag() {
      this.$toast(this.$t('goods.tag'));
    },
    getAddress() {
      this.$http({
        method: 'post',
        url: 'xuanfei_address'
      }).then(res => {
        if (res.code === 200) {
          this.addressList = res.data
        } else if (res.code === 401) {
          this.$toast(res.msg);
        }
      })
    },
    getXuanfeiList() {
      this.$http({
        method: 'get',
        url: 'xuanfei_list',
        data: this.search
      }).then(res => {
        if (res.code === 200) {
          console.log(res)
          this.allXuanfeiList = res.data.data.map(item => {
            item.zan = parseInt(Math.random() * 30000 + 20000)
            return item
          })
          this.ageList = Array.from(new Set([...res.data.data.map(item => item.age)])).sort((a, b) => a - b)
          this.age = null
        } else if (res.code === 401) {
          this.$toast(res.msg);
        }
      })
    },

    goDetail(e) {
      localStorage.setItem('xuanfeiDetail', JSON.stringify(e))
      this.$router.push(`XuanfeiDetail`);
    }
  }
};
</script>

<style lang="less" scoped>
.main {
  padding: 0;
  background: none;
  background: url('/public/img/bg.png') repeat top / 100% auto;
  .head {
    display: flex;
    justify-content: center;
    height: 2.88rem;
    font-weight: bold;
    align-items: center;
  }
  .gif {
    width: 96%;
    margin: 0.38rem auto;
    border-radius: .8rem;
    overflow: hidden;
    img{
      width: 100%;
      height: 6rem;
      display: block;
    }
  }

  .select_wrap{
    display: flex;
    flex-wrap: wrap;
    div{
      font-size: .76667rem;
      padding: .2rem 0;
      text-align: center;
      width: calc(100% / 5);
      color: #646566;
      transition: all .2s;
      cursor: pointer;
      margin-bottom: .33333rem;
    }
    .active{
      color: #333;
      background: #e2e2e2;
      transform: scale(1.1);
      border-bottom: .06667rem solid #b979b1;
    }
  }
  
  #content{
    padding-bottom: 3.2rem;
    padding-top: 0;
  }


  .view {
    width: 100%;
    height: calc(var(--vh) * 100 - 2.88rem - 2.9rem - 3.13rem);
    overflow-y: auto;
    padding: 0 2%;
    display: flex;
    flex-wrap: wrap;

    >div {
      padding-top: 3%;
      width: 48%;
      margin: 0 auto;
    }

    .card {
      border-radius: 0.75rem;
      position: relative;
      margin-bottom: 8%;
      height: 20rem;
      background: #fff;
      padding: 0.44rem;
      box-shadow: 0 .33333rem .66667rem rgba(255, 0, 110, .11);
      .img_wrap{
        border-radius: .625rem;
        overflow: hidden;
        position: relative;
        img {
          width: 100%;
          height: 11.25rem;
          object-fit: cover;
          display: block;
          border-radius: 0.625rem;
        }
        .like{
          background: url('/public/img/goods/like.png');
          background-size: 100% 100%;
          width: 2.00rem;
          height: 2.00rem;
          position: absolute;
          left: .3rem;
          top: .3rem;
          z-index: 1;
        }
        .tag {
          background: url('/public/img/goods/tag.png');
          background-size: 100% 100%;
          width: 3.00rem !important;
          height: 1.75rem !important;
          position: absolute;
          right: 0;
          top: 0;
          color: #fff;
          font-size: 0.88rem;
          display: flex;
          align-items: center;
          justify-content: center;
          padding-left: .4rem;
        }
        .shadow{
          height: 2.00rem;
          width: 100%;
          position: absolute;
          bottom: 0;
          background: rgba(0, 0, 0, .4);
          display: flex;
          justify-content: space-between;
          padding: 0.38rem 0.63rem;
          font-size: 0.85rem;
          color: #fff;
          .btn{
            border: 0.06rem solid #fff;
            padding: 0.08rem 0.38rem;
            border-radius: .13333rem;
            opacity: .9;
            font-size: 0.75rem;
          }
        }
      }
      .row{
        font-size: 0.88rem;
        margin-top: 0.31rem;
        color: #666;
        padding-top: 0.13rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        >div {
          display: flex;
          align-items: center;
          gap: 0.50rem;
        }
        img {
          width: 1.75rem;
          height: 1.75rem;
          border-radius: 50%;
          display: block;
          object-fit: contain;
        }
        .right {
          img {
            width: 1.00rem;
            height: 1.00rem;
          }
        }
        .circle{
          display: inline-block;
          margin-right: .2rem;
          width: .8rem;
          height: .8rem;
          border-radius: 50%;
          background: #efba8f;
        }
      }


      .info {
        position: absolute;
        bottom: 5%;
        width: 100%;

        .text {
          font-size: 1.3rem;
          text-align: center;
          color: #fff;
          font-weight: 600;
        }

        .bottom {
          color: #fff;
          background-color: #ff1a6d;
          width: 60%;
          font-size: .9rem;
          margin: 0 auto;
          margin-top: 5%;
          display: flex;
          justify-content: center;
          align-items: center;
          padding-top: 3%;
          padding-bottom: 3%;
          border-radius: 0.9375rem;
        }
      }
      
      .biaoqian {
        display: flex;
        flex-wrap: wrap;
        padding: 0.3rem 0;

        div {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: .2rem .4rem;
          margin: .16667rem .16667rem;
          color: #fff;
          border-radius: .33333rem;
          font-size: .76667rem;

          &:nth-child(1n) {
            background-color: #fe257f;
          }

          &:nth-child(2n) {
            background-color: #ff9702;
          }

          &:nth-child(3n) {
            background-color: #0fa7fe;
          }
        }
      }

      .xingji {
        color: #f4b757;
        font-size: .9rem;
        padding-bottom: .3rem;
      }
      
      .button {
        text-align: center;
        background: linear-gradient(90deg,#fa6ba6,#f32879);
        color: #fff;
        height: 1.69rem;
        line-height: 1.69rem;
        border-radius: 1rem;
        // padding: .33333rem 0;
        width: 70%;
        margin: .13333rem auto ;
      }
    }
  }
}


::v-deep .van-tabs__nav {
  background: transparent;
  gap: 0.63rem;
  align-items: center;
}
::v-deep .van-tab {
  color: #442889;
  background: #fff;
  border-radius: 5.63rem;
  height: 1.75rem;
  font-weight: 400;
  font-size: 0.88rem;
}
::v-deep .van-tabs--line .van-tabs__wrap {
  height: 2.75rem;
}
::v-deep .van-tabs__wrap--scrollable .van-tab {
  padding: 0 1rem;
}
::v-deep  .van-hairline--bottom::after {
  border-bottom-width: 0.00rem;
}
::v-deep .van-tabs__line {
  display: none;
}
::v-deep  .van-tab--active {
  font-weight: bold;
  font-size: 1.06rem;
  background: linear-gradient(180deg, #fc78af, #f22678);
  color: #fff;
  box-shadow: 0 0.13rem 0.38rem rgba(242, 38, 120, .25);
}
</style>
