<template>
  <div class="container">
    <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('user.icon8') }}</span>
      </template>
    </van-nav-bar>
    
    <div class="wrap" id="content">
      <van-pull-refresh v-model="refresherStatus" @refresh="refresher" class="container" :pulling-text="$t('common.pullLabel1')" :loosing-text="$t('common.pullLabel2')" :loading-text="$t('common.pullLabel3')">
        <div class="card" v-for="(item,index) in gameRecordList" :key="index" @click="goPage(item)">
          <div class="icon">$</div>
          <div class="right">
            <div class="row">
              <div class="title">{{ item.status_text }}</div>
              <div class="label">{{ item.create_time }}</div>
            </div>
            <div class="row">
              <div class="price" :class="{green: item.money > 0, red: item.money <= 0}"><span v-show="item.money >= 0">+</span>{{ changeMoney(item.money) }}</div>
            </div>
          </div>
        </div>
        <Nodata v-if="gameRecordList.length === 0" />
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
import Nodata from "@/common/Nodata.vue";
import { changeMoney } from "@/common/function";
export default {
  components: {Nodata},
  data() {
    return {
      active: 0,
      refresherStatus: '',
      statusMap: new Map([
        [0, 'all'],
        [1, 'win'],
        [2, 'lose'],
        [3, 'no_open'],
      ]),
      gameRecordList: []
    };
  },
  created() {
    this.getLiushui()
  },
  methods: {
    getLiushui(){
      return this.$http({
        method: 'get',
        url: 'user_get_withdraw_list',
        data: this.params
      }).then(res=>{
        this.gameRecordList = res.data
      })
    },
    back(){
      return window.history.back();
    },

    async refresher() {
      this.refresherStatus = true
      Toast.loading({forbidClick: true})
      await this.getLiushui()
      Toast.clear()
      this.refresherStatus = false
    },

  }
};
</script>

<style lang='less' scoped>
.van-nav-bar {
  height: 2.88rem;
  background: linear-gradient(20deg, #a253d0, #d63f8c);
}

::v-deep .van-tabs {
  .van-tab, .van-tabs__wrap {
    height: 2.75rem;
  }
  .van-tab__text{
    font-size: 0.88rem;
    line-height: 2.75rem;
    color: #6f41b9;
  }
  .van-tab--active .van-tab__text{
    font-weight: bold;
  }
}

.container{
  height: 100%;
  background: #f0f0f0;
}

.wrap{
  // overflow-y: scroll;
  // height: calc(var(--vh) * 100 - 2.8125rem - 5%);
}
#content{
  .container {
    height: calc(var(--vh) * 100 - 2.88rem - 3.05rem );
    overflow-y: auto;
  }
  .card{
    width: 100%;
    margin: 0.63rem auto 0.31rem;
    border-radius: 0.50rem;
    background: #fff;
    padding: 0.63rem 1.00rem;
    display: flex;
    justify-content: space-between;
    .right {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .row{
      // height: 1.50rem;
      // line-height: 1.50rem;
      // display: flex;
      justify-content: space-between
    }
    .icon {
      width: 2.88rem;
      height: 2.88rem;
      line-height: 2.88rem;
      border-radius: 6.25rem;
      background: #f16290;
      color: #fff;
      text-align: center;
      margin-right: 0.63rem;
    }
    .title {
      font-size: 0.88rem;
      color: #49328b;
    }
    .label {
      font-size: 0.75rem;
      color: #49328b;
    }
    .remark {
      font-size: 0.81rem;
      color: #b2a8cd;
      text-align: right;
      line-height: 1.50rem;
    }
    .price {
      font-size: 1.00rem;
      text-align: right;
      line-height: 1.50rem;
      font-weight: bold;
    }
    .green{
      color: #36c989;
    }
    .red{
      color: rgb(242, 96, 96);
    }
  }
}

</style>
