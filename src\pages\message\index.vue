<template>
  <div class="container">
    <van-nav-bar>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('message.title') }}</span>
      </template>
    </van-nav-bar>
    
    <div class="wrap" id="content">


      <van-pull-refresh v-model="isLoading" @refresh="refresher" class="container" :pulling-text="$t('common.pullLabel1')" :loosing-text="$t('common.pullLabel2')" :loading-text="$t('common.pullLabel3')">
        <Nodata v-if="liushuiList.length === 0" />
        <van-list v-model="loading" :finished="finished" :finished-text="$t('common.nomore')" :loading-text="$t('common.loading')" :error-text="$t('common.error')" @load="onLoad">
          <div class="wrap">
            <div class="card" v-for="(item,index) in liushuiList" :key="index"  @click="goPage(item)">
              <div class="left">
                <div class="title">{{ item.title }}</div>
                <div class="time">{{ item.content }}</div>
              </div>
              <div class="status">{{ item.author }}</div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>


    </div>
  </div>
</template>

<script>
import message from './message.json'
import Nodata from "@/common/Nodata.vue";
export default {
  components: { Nodata },
  data() {
    return {
      liushuiList:[],
      page:0,
      finished: false,
      loading: false,
      isLoading: false
    };
  },
  created() {
    console.log(message)
    // this.liushuiList = message.data
    // this.getLiushuiList()
  },
  methods: {
    goPage(e) {
      localStorage.setItem('messageDetail', JSON.stringify(e))
      this.$router.push(`/MessageDetail`)
    },
    
    async getLiushuiList(refresh=true){
      return this.$http({
        method: 'get',
        data:{id:this.type,page:this.page},
        url: 'message_list'
      }).then(res=>{
        this.total = res.data.total;
        if(refresh) {
          this.liushuiList = res.data.data
        }else {
          this.liushuiList = [...this.liushuiList, ...res.data.data]
        }
        this.loading = false
        if(this.liushuiList.length >= this.total) {
          this.finished = true
        }
      })
    },

    onLoad() {
      console.log('onload')
      this.page ++
      this.getLiushuiList(false);
    },
    
    async refresher() {
      this.isLoading = true
      this.page = 1
      await this.getLiushuiList()
      this.finished = false
      this.isLoading = false
    },
  }
};
</script>

<style lang='less' scoped>
.van-nav-bar {
  height: 2.88rem;
}

::v-deep .van-tabs {
  .van-tab, .van-tabs__wrap {
    height: 2.75rem;
  }
  .van-tab__text{
    font-size: 0.88rem;
    line-height: 2.75rem;
    color: #6f41b9;
  }
  .van-tab--active .van-tab__text{
    font-weight: bold;
  }
}

.container{
  height: 100%;
  background: #f0f0f0;
}

.wrap{
  .container {
    height: calc(var(--vh) * 100 - 2.88rem);
    overflow-y: auto;
  }
  .card{
    width: 100%;
    height: 4.13rem;
    padding: 0.63rem 1.00rem;
    border-radius: 0.50rem;
    background: #fff;
    padding: 0.63rem 1.00rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before{
      position: absolute;
      content: "";
      border: none;
      width: 90%;
      height: 0.06rem;
      background: linear-gradient(90deg, rgba(80, 59, 143, .15), rgba(80, 59, 143, .61), rgba(80, 59, 143, .15));
      bottom: 0;
    }
    .left{
      width: 60%;
    }
    .title {
      font-size: 0.88rem;
      color: #49328b;
      font-weight: bold;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .time{
      font-size: 0.75rem;
      color: #969799;
      margin-top: 0.25rem;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .status {
      background: #f33ddb;
      color: #fff;
      font-size: 0.69rem;
      padding: 0 0.50rem;
      border-radius: 0.25rem;
      height: 2.00rem;
      line-height: 2rem;
    }
  }
}

::v-deep .van-list__finished-text{
  font-size: 1.00rem !important;
  height: 3.13rem;
  line-height: 3.13rem;
}
::v-deep .van-loading__text {
  font-size: 1.00rem !important;
}

</style>
