<template>
  <div class="bg-container">
    <div class="login">
      <div class="head">
        <van-popover v-model="showPopover" trigger="click" :actions="actions" @select="onSelect">
          <template #reference>
            <van-button type="primary" plain>{{languageText}}</van-button>
            <div></div>
          </template>
        </van-popover>
      </div>
      <div class="logo">
        <img src="/img/login/logo.png" alt="">
      </div>
      <div class="wrap">
        <div class="input_wrap">
          <img src="/img/login/user.png" alt="" style="width: 1.3rem; height: 1.3rem;">
          <input type="text" :placeholder="$t('auth.username_place')" v-model="username">
        </div>
        <div class="input_wrap">
          <img src="/img/login/pwd.png" alt="" style="width: 1.3rem; height: 1.3rem;">
          <input :placeholder="$t('auth.pwd_place')" v-model="password" :type="pwdType == 1 ? 'password' : 'text'">
          <van-icon :name="pwdType == 0 ? 'eye-o' : 'closed-eye'" color="#FF58D2" size="24" @click="pwdType = pwdType == 1 ? 0 : 1"></van-icon>
        </div>
      </div>
      <div class="button" @click="doLogin()">{{$t("auth.login")}}</div>
      <div class="row" @click="toRegister">
        <span>{{$t("auth.no_account1")}}</span>
        <span style="color: #F100FF; text-decoration: underline;">{{$t("auth.no_account2")}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from "vant";
export default {
  model: {
    prop: "inputValue",
    event: "input",
  },
  props: {
    /**
     * 当前输入的值
     */
    inputValue: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      pwdType: 1,
      username: "",
      lang: 'en_us',
      password: this.inputValue,
      passwordType: "password",
      showPopover: false,
      languageText: '',
      actions: [{ text: '繁体中文', code: 'zh_tw' }, { text: 'English', code: 'en_us' }, { text: '한국인', code: 'kor' }],
    };
  },
  mounted(){
    // localStorage.setItem("lang", 'ms_my');
    this.lang = localStorage.getItem("lang") || 'zh_tw';
    this.actions.map(item => {
      if(item.code === this.lang) {
        this.languageText = item.text
      }
    })
  },
  methods: {
    onSelect(e) {
      Toast.loading({
        duration: 200,
      });
      this.$i18n.locale = e.code;
      this.languageText = e.text;
      localStorage.setItem("lang", e.code);
    },
    switchPasswordType() {
      this.passwordType =
        this.passwordType === "password" ? "text" : "password";
    },
    back() {
      return window.history.back();
    },
    toRegister() {
      this.$router.push("Register");
    },
    doLogin() {
      if (
        this.username === "" ||
        this.username === null ||
        this.username === undefined
      ) {
        this.$toast(this.$t("auth.username_place"));
        return false;
      }
      if (
        this.password === "" ||
        this.password === null ||
        this.password === undefined
      ) {
        this.$toast(this.$t("auth.pwd_place"));
        return false;
      }
      this.$http({
        url: "member_login",
        method: "post",
        data: { 
          username: this.username, 
          password: this.password,
          lang: this.lang
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$toast.success(res.msg);
          localStorage.setItem("token", res.data.id);
          this.$router.push("Home");
        } else {
          this.$toast(res.msg);
        }
      });
    },
  },
  created() {
    if (localStorage.getItem("token")) {
      return window.history.back();
    }
  },
};
</script>

<style lang='less' scoped>
.bg-container {
  background-image: linear-gradient(180deg,#c7c7c7,#9c9c9c);
  height: 100%;
}
.login {
  height: 100%;
  width: 100%;
  background: url('/public/img/login/login.png') no-repeat 50% / 100%;
  overflow: hidden;
}

.logo {
  width: 13.50rem;
  margin: 5.00rem auto 0.63rem;
  img{
    width: 100%;
  }
}

.wrap{
  .input_wrap{
    width: 90%;
    height: 3.66667rem;
    margin: 1rem auto;
    border-radius: 0.88rem;
    overflow: hidden;
    background: #3E004088;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 0.94rem;
    input{
      flex: 1;
      height: 100%;
      border: none;
      background: none;
      outline: none;
      text-align: left;
      font-size: .93333rem;
      padding-left: 1rem;
      color: #fff;
      &::placeholder {
        color: #ccc;
      }
    }
  }
}

.row {
  font-size: 1rem;
  color: #fff;
  margin-top: 4.18rem;
  text-align: center;
}

.button{
  width: 90%;
  background: url('/public/img/login/button_bg.png') no-repeat 50% / 100%;
  font-size: 1.19rem;
  color: #fff;
  text-align: center;
  height: 4.25rem;
  line-height: 4.25rem;
  margin: 0 auto;
  margin-top: 2.50rem;
}
.head{
  padding: 1.13rem 0.75rem;
  display: flex;
  justify-content: flex-end;
  position: fixed;
  top: 0;
  width: 100%;
}
.van-button {
  padding: 0 0.63rem;
  font-size: 0.88rem;
  background: transparent;
  border-color: #fff;
  color: #fff;
  border-radius: 0.28rem;
}
::v-deep .van-popover__action-text {
  font-size: 0.88rem;
}
</style>
