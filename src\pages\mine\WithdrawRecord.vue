<template>
  <div class="container">
    <van-nav-bar :title="$t('withdraw.with_record')">
      <template #left>
        <van-icon name="arrow-left" color="#fff" size="20"  @click="back()"></van-icon>
      </template>
    </van-nav-bar>

    
    <div class="rows" id="content">
      <div class="row" v-for="(item, index) in recordList" :key="index">
        <div class="left">
          <div class="title">{{ $t('withdraw.money') }}: {{changeMoney(item.money)}}KRW</div>
          <div class="time">{{ item.create_time }}</div>
        </div>
        <div class="right">{{ item.status_text }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { changeMoney } from '@/common/function';

export default {
  data() {
    return {
      recordList: [],
    };
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getRechargeRecord();
    }
    
  },
  methods: {
    back(){
      return window.history.back();
    },
    getRechargeRecord(){
      this.$http({
        method: 'get',
        url: 'user_get_withdraw_list'
      }).then(res=>{
        if(res.code === 200){
          console.log(res.data)
          this.recordList = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    }
  }
};
</script>

<style lang='less' scoped>
.van-nav-bar {
  height: 2.88rem;

  /deep/ .van-nav-bar__title{
    color: #fff;
    font-size: 1rem;
  }
  /deep/ .van-nav-bar__left{
    padding: 0 1rem;
  }
}

.rows{
  margin-bottom: 1.5625rem;
  .row{
    display: flex;
    padding: 0.63rem 1.00rem;
    position: relative;
    &::before{
      position: absolute;
      content: "";
      border: none;
      width: 90%;
      height: 0.06rem;
      background: linear-gradient(90deg, rgba(80, 59, 143, .15), rgba(80, 59, 143, .61), rgba(80, 59, 143, .15));
      bottom: 0;
    }
    >div{
      flex: 1;
      height: 2.88rem;
    }
    .left{
      display: flex;
      justify-content: space-around;
      flex-direction: column;
    }
    .right{
      display: flex;
      align-items: center;
      color: #ff0000;
      font-size: 0.88rem;
      justify-content: flex-end;
    }
    .title{
      font-size: 0.88rem;
      color: #49328b;
    }
    .time{
      font-size: 0.75rem;
      color: #969799;
      margin-top: 0.25rem;
    }
  }
}

</style>
