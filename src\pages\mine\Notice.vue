<template>
  <div class="container">
    <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('my.sys_notice') }}</span>
      </template>
    </van-nav-bar>
    
    <div class="wrap" id="content">
      <div class="card" v-for="(item,index) in notice" :key="index">
        <div class="title">{{ item.name }}</div>
        <div class="date"><p>{{ item.create_time }}<br></p></div>
        <div class="message">{{ item.text }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false,
      notice:[],
      aaa: {
        "code": 200,
        "msg": "\uc644\ub8cc!",
        "sdata": [
            {
                "id": 8,
                "name": "\u203b\uacf5\uc9c0\u203b\ucd9c\uc7a5 \uc544\uac00\uc528 \uc54c\ubc14\uc0dd\ubaa8\uc9d1(\uc7a5\uae30\uc720\ud6a8)",
                "name_zh_cn": "\u203b\uacf5\uc9c0\u203b\ucd9c\uc7a5 \uc544\uac00\uc528 \uc54c\ubc14\uc0dd\ubaa8\uc9d1(\uc7a5\uae30\uc720\ud6a8)",
                "name_en_us": "\u203b\uacf5\uc9c0\u203b\ucd9c\uc7a5 \uc544\uac00\uc528 \uc54c\ubc14\uc0dd\ubaa8\uc9d1(\uc7a5\uae30\uc720\ud6a8)",
                "name_es_spa": "\u203b\uacf5\uc9c0\u203b\ucd9c\uc7a5 \uc544\uac00\uc528 \uc54c\ubc14\uc0dd\ubaa8\uc9d1(\uc7a5\uae30\uc720\ud6a8)",
                "name_ms_my": "\u203b\uacf5\uc9c0\u203b\ucd9c\uc7a5 \uc544\uac00\uc528 \uc54c\ubc14\uc0dd\ubaa8\uc9d1(\uc7a5\uae30\uc720\ud6a8)",
                "name_kor": null,
                "status": 1,
                "text": "\ub9e4\ub2c8\uc800 \ubaa8\uc9d1(\uc0c1\uc2dc\ubaa8\uc9d1)\r\n\r\n\ub2e8\uae30,\uc7a5\uae30 \ub9e4\ub2c8\uc800 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. (\ucd9c\uc7a5, \uc560\uc778\ub300\ud589, \uc870\uac74\ub9cc\ub0a8,) \r\n\r\n1. \uc5bc\uad74 \ubab8\ub9e4\uc5d0 \uc790\uc2e0\uc788\ub294 \uc5ec\uc131\ubd84\ub4e4 \ub9dd\uc124\uc774\uc9c0\ub9d0\uace0 \uc5f0\ub77d\uc8fc\uc138\uc694.\r\n2. \ub098\uc774 20~40\uc138\uae4c\uc9c0 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. \r\n\r\n\uc219\uc2dd\uc81c\uacf5 \ub429\ub2c8\ub2e4. \uc774\uc678 \ud544\uc694\ud55c \ube44\ud488 \uc9c0\uc6d0 \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\ub9e4\ub2c8\uc800 \ubd80\uc871\ud604\uc0c1\uc73c\ub85c \uc778\ud574 \uc0c1\uc2dc \ubaa8\uc9d1 \uc911\uc785\ub2c8\ub2e4.\r\n\uac2f\uc218 \ub9de\ucdb0 \ub4dc\ub9bd\ub2c8\ub2e4. (1\uc778 \uc6d4 \ud3c9\uade0  800~1000 \ub9de\ucdb0\ub4dc\ub9bd\ub2c8\ub2e4.)\r\n\r\n\uc6d0\ud558\ub294 \uc9c0\uc5ed \uc804\uad6d \uc5b4\ub514\ub4e0 \uc120\ud0dd \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\uc9c0\uc6d0\uc744 \uc6d0\ud558\uc2dc\uba74 \uc790\uae30\uc18c\uac1c\uc640 \ubcf8\uc778 \uc778\uc99d \ud560 \uc0ac\uc9c4\uc774 \ud544\uc694\ud569\ub2c8\ub2e4.\r\n\ub9ce\uc740 \uc5f0\ub77d \ubd80\ud0c1\ub4dc\ub9bd\ub2c8\ub2e4.",
                "text_zh_cn": "\ub9e4\ub2c8\uc800 \ubaa8\uc9d1(\uc0c1\uc2dc\ubaa8\uc9d1)\r\n\r\n\ub2e8\uae30,\uc7a5\uae30 \ub9e4\ub2c8\uc800 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. (\ucd9c\uc7a5, \uc560\uc778\ub300\ud589, \uc870\uac74\ub9cc\ub0a8,) \r\n\r\n1. \uc5bc\uad74 \ubab8\ub9e4\uc5d0 \uc790\uc2e0\uc788\ub294 \uc5ec\uc131\ubd84\ub4e4 \ub9dd\uc124\uc774\uc9c0\ub9d0\uace0 \uc5f0\ub77d\uc8fc\uc138\uc694.\r\n2. \ub098\uc774 20~40\uc138\uae4c\uc9c0 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. \r\n\r\n\uc219\uc2dd\uc81c\uacf5 \ub429\ub2c8\ub2e4. \uc774\uc678 \ud544\uc694\ud55c \ube44\ud488 \uc9c0\uc6d0 \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\ub9e4\ub2c8\uc800 \ubd80\uc871\ud604\uc0c1\uc73c\ub85c \uc778\ud574 \uc0c1\uc2dc \ubaa8\uc9d1 \uc911\uc785\ub2c8\ub2e4.\r\n\uac2f\uc218 \ub9de\ucdb0 \ub4dc\ub9bd\ub2c8\ub2e4. (1\uc778 \uc6d4 \ud3c9\uade0  800~1000 \ub9de\ucdb0\ub4dc\ub9bd\ub2c8\ub2e4.)\r\n\r\n\uc6d0\ud558\ub294 \uc9c0\uc5ed \uc804\uad6d \uc5b4\ub514\ub4e0 \uc120\ud0dd \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\uc9c0\uc6d0\uc744 \uc6d0\ud558\uc2dc\uba74 \uc790\uae30\uc18c\uac1c",
                "text_en_us": "\ub9e4\ub2c8\uc800 \ubaa8\uc9d1(\uc0c1\uc2dc\ubaa8\uc9d1)\r\n\r\n\ub2e8\uae30,\uc7a5\uae30 \ub9e4\ub2c8\uc800 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. (\ucd9c\uc7a5, \uc560\uc778\ub300\ud589, \uc870\uac74\ub9cc\ub0a8,) \r\n\r\n1. \uc5bc\uad74 \ubab8\ub9e4\uc5d0 \uc790\uc2e0\uc788\ub294 \uc5ec\uc131\ubd84\ub4e4 \ub9dd\uc124\uc774\uc9c0\ub9d0\uace0 \uc5f0\ub77d\uc8fc\uc138\uc694.\r\n2. \ub098\uc774 20~40\uc138\uae4c\uc9c0 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. \r\n\r\n\uc219\uc2dd\uc81c\uacf5 \ub429\ub2c8\ub2e4. \uc774\uc678 \ud544\uc694\ud55c \ube44\ud488 \uc9c0\uc6d0 \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\ub9e4\ub2c8\uc800 \ubd80\uc871\ud604\uc0c1\uc73c\ub85c \uc778\ud574 \uc0c1\uc2dc \ubaa8\uc9d1 \uc911\uc785\ub2c8\ub2e4.\r\n\uac2f\uc218 \ub9de\ucdb0 \ub4dc\ub9bd\ub2c8\ub2e4. (1\uc778 \uc6d4 \ud3c9\uade0  800~1000 \ub9de\ucdb0\ub4dc\ub9bd\ub2c8\ub2e4.)\r\n\r\n\uc6d0\ud558\ub294 \uc9c0\uc5ed \uc804\uad6d \uc5b4\ub514\ub4e0 \uc120\ud0dd \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\uc9c0\uc6d0\uc744 \uc6d0\ud558\uc2dc\uba74 \uc790\uae30\uc18c\uac1c",
                "text_es_spa": "\ub9e4\ub2c8\uc800 \ubaa8\uc9d1(\uc0c1\uc2dc\ubaa8\uc9d1)\r\n\r\n\ub2e8\uae30,\uc7a5\uae30 \ub9e4\ub2c8\uc800 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. (\ucd9c\uc7a5, \uc560\uc778\ub300\ud589, \uc870\uac74\ub9cc\ub0a8,) \r\n\r\n1. \uc5bc\uad74 \ubab8\ub9e4\uc5d0 \uc790\uc2e0\uc788\ub294 \uc5ec\uc131\ubd84\ub4e4 \ub9dd\uc124\uc774\uc9c0\ub9d0\uace0 \uc5f0\ub77d\uc8fc\uc138\uc694.\r\n2. \ub098\uc774 20~40\uc138\uae4c\uc9c0 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. \r\n\r\n\uc219\uc2dd\uc81c\uacf5 \ub429\ub2c8\ub2e4. \uc774\uc678 \ud544\uc694\ud55c \ube44\ud488 \uc9c0\uc6d0 \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\ub9e4\ub2c8\uc800 \ubd80\uc871\ud604\uc0c1\uc73c\ub85c \uc778\ud574 \uc0c1\uc2dc \ubaa8\uc9d1 \uc911\uc785\ub2c8\ub2e4.\r\n\uac2f\uc218 \ub9de\ucdb0 \ub4dc\ub9bd\ub2c8\ub2e4. (1\uc778 \uc6d4 \ud3c9\uade0  800~1000 \ub9de\ucdb0\ub4dc\ub9bd\ub2c8\ub2e4.)\r\n\r\n\uc6d0\ud558\ub294 \uc9c0\uc5ed \uc804\uad6d \uc5b4\ub514\ub4e0 \uc120\ud0dd \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\uc9c0\uc6d0\uc744 \uc6d0\ud558\uc2dc\uba74 \uc790\uae30\uc18c\uac1c",
                "text_ms_my": "\ub9e4\ub2c8\uc800 \ubaa8\uc9d1(\uc0c1\uc2dc\ubaa8\uc9d1)\r\n\r\n\ub2e8\uae30,\uc7a5\uae30 \ub9e4\ub2c8\uc800 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. (\ucd9c\uc7a5, \uc560\uc778\ub300\ud589, \uc870\uac74\ub9cc\ub0a8,) \r\n\r\n1. \uc5bc\uad74 \ubab8\ub9e4\uc5d0 \uc790\uc2e0\uc788\ub294 \uc5ec\uc131\ubd84\ub4e4 \ub9dd\uc124\uc774\uc9c0\ub9d0\uace0 \uc5f0\ub77d\uc8fc\uc138\uc694.\r\n2. \ub098\uc774 20~40\uc138\uae4c\uc9c0 \ubaa8\uc9d1\ud569\ub2c8\ub2e4. \r\n\r\n\uc219\uc2dd\uc81c\uacf5 \ub429\ub2c8\ub2e4. \uc774\uc678 \ud544\uc694\ud55c \ube44\ud488 \uc9c0\uc6d0 \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\ub9e4\ub2c8\uc800 \ubd80\uc871\ud604\uc0c1\uc73c\ub85c \uc778\ud574 \uc0c1\uc2dc \ubaa8\uc9d1 \uc911\uc785\ub2c8\ub2e4.\r\n\uac2f\uc218 \ub9de\ucdb0 \ub4dc\ub9bd\ub2c8\ub2e4. (1\uc778 \uc6d4 \ud3c9\uade0  800~1000 \ub9de\ucdb0\ub4dc\ub9bd\ub2c8\ub2e4.)\r\n\r\n\uc6d0\ud558\ub294 \uc9c0\uc5ed \uc804\uad6d \uc5b4\ub514\ub4e0 \uc120\ud0dd \uac00\ub2a5\ud569\ub2c8\ub2e4.\r\n\uc9c0\uc6d0\uc744 \uc6d0\ud558\uc2dc\uba74 \uc790\uae30\uc18c\uac1c",
                "text_kor": null,
                "hot": 1,
                "create_time": "2023-02-05 00:29:20",
                "update_time": 1725804176
            },
            {
                "id": 9,
                "name": "\u203b\uacf5\uc9c0\u203b \u203b\uc989\uc11d\ub9cc\ub0a8\uc2b9\uc778\uc2e0\uccad\u203b",
                "name_zh_cn": "\u203b\uacf5\uc9c0\u203b \u203b\uc989\uc11d\ub9cc\ub0a8\uc2b9\uc778\uc2e0\uccad\u203b",
                "name_en_us": "\u203b\uacf5\uc9c0\u203b \u203b\uc989\uc11d\ub9cc\ub0a8\uc2b9\uc778\uc2e0\uccad\u203b",
                "name_es_spa": "\u203b\uacf5\uc9c0\u203b \u203b\uc989\uc11d\ub9cc\ub0a8\uc2b9\uc778\uc2e0\uccad\u203b",
                "name_ms_my": "\u203b\uacf5\uc9c0\u203b \u203b\uc989\uc11d\ub9cc\ub0a8\uc2b9\uc778\uc2e0\uccad\u203b",
                "name_kor": null,
                "status": 1,
                "text": "\uc548\uc804\ud568 \ub9cc\ub0a8\uc744 \uc704\ud574 \uc608\uc57d\uc81c\ub85c \ub9cc\ub0a8\uc774 \uc774\ub8e8\uc5b4\uc9d1\ub2c8\ub2e4. \r\n\ucd08\uac1d\uc758 \uacbd\uc6b0 \uc778\uc99d\uc744 \uc644\ub8cc \ud558\uc154\uc57c \ud569\ub2c8\ub2e4.",
                "text_zh_cn": "\uc548\uc804\ud568 \ub9cc\ub0a8\uc744 \uc704\ud574 \uc608\uc57d\uc81c\ub85c \ub9cc\ub0a8\uc774 \uc774\ub8e8\uc5b4\uc9d1\ub2c8\ub2e4. \r\n\ucd08\uac1d\uc758 \uacbd\uc6b0 \uc778\uc99d\uc744 \uc644\ub8cc \ud558\uc154\uc57c \ud569\ub2c8\ub2e4.",
                "text_en_us": "\uc548\uc804\ud568 \ub9cc\ub0a8\uc744 \uc704\ud574 \uc608\uc57d\uc81c\ub85c \ub9cc\ub0a8\uc774 \uc774\ub8e8\uc5b4\uc9d1\ub2c8\ub2e4. \r\n\ucd08\uac1d\uc758 \uacbd\uc6b0 \uc778\uc99d\uc744 \uc644\ub8cc \ud558\uc154\uc57c \ud569\ub2c8\ub2e4.",
                "text_es_spa": "\uc548\uc804\ud568 \ub9cc\ub0a8\uc744 \uc704\ud574 \uc608\uc57d\uc81c\ub85c \ub9cc\ub0a8\uc774 \uc774\ub8e8\uc5b4\uc9d1\ub2c8\ub2e4. \r\n\ucd08\uac1d\uc758 \uacbd\uc6b0 \uc778\uc99d\uc744 \uc644\ub8cc \ud558\uc154\uc57c \ud569\ub2c8\ub2e4.",
                "text_ms_my": "\uc548\uc804\ud568 \ub9cc\ub0a8\uc744 \uc704\ud574 \uc608\uc57d\uc81c\ub85c \ub9cc\ub0a8\uc774 \uc774\ub8e8\uc5b4\uc9d1\ub2c8\ub2e4. \r\n\ucd08\uac1d\uc758 \uacbd\uc6b0 \uc778\uc99d\uc744 \uc644\ub8cc \ud558\uc154\uc57c \ud569\ub2c8\ub2e4.",
                "text_kor": null,
                "hot": 0,
                "create_time": "2023-05-13 19:47:10",
                "update_time": 1725804345
            },
            {
                "id": 10,
                "name": "\u203b\uacf5\uc9c0\u203b \uc2a4\ud3f0\uc11c \uc9c0\uc6d0 \uac00\ub2a5\ud55c \ud2b9\uae09 \uace0\uac1d\ub2d8\ub4e4\uc744 \uc704\ud558\uc5ec \uc911",
                "name_zh_cn": "\u203b\uacf5\uc9c0\u203b \uc2a4\ud3f0\uc11c \uc9c0\uc6d0 \uac00\ub2a5\ud55c \ud2b9\uae09 \uace0\uac1d\ub2d8\ub4e4\uc744 \uc704\ud558\uc5ec \uc911\ub9e4",
                "name_en_us": "\u203b\uacf5\uc9c0\u203b \uc2a4\ud3f0\uc11c \uc9c0\uc6d0 \uac00\ub2a5\ud55c \ud2b9\uae09 \uace0\uac1d\ub2d8\ub4e4\uc744 \uc704\ud558\uc5ec \uc911\ub9e4",
                "name_es_spa": "\u203b\uacf5\uc9c0\u203b \uc2a4\ud3f0\uc11c \uc9c0\uc6d0 \uac00\ub2a5\ud55c \ud2b9\uae09 \uace0\uac1d\ub2d8\ub4e4\uc744 \uc704\ud558\uc5ec \uc911\ub9e4",
                "name_ms_my": "\u203b\uacf5\uc9c0\u203b \uc2a4\ud3f0\uc11c \uc9c0\uc6d0 \uac00\ub2a5\ud55c \ud2b9\uae09 \uace0\uac1d\ub2d8\ub4e4\uc744 \uc704\ud558\uc5ec \uc911\ub9e4",
                "name_kor": null,
                "status": 1,
                "text": "\uc2a4\ud3f0 \ub9e4\uce6d \uc9c0\uc6d0 \uc11c\ube44\uc2a4\r\n\r\n\uc7a5\uae30\ub9cc\ub0a8(\uc2a4\ud3f0) \ub9e4\uce6d \uac00\ub2a5\ud569\ub2c8\ub2e4. \r\n\ucd5c\uc18c\uae30\uac04 \ud55c\ub2ec\ubd80\ud130 \uc2dc\uc791\ub429\ub2c8\ub2e4. \ub9cc\ub0a8 \uc774\ud6c4 \uc7a5\uae30 \ub9cc\ub0a8 \ud76c\ub9dd \ud558\uc2dc\ub294 \ubd84\ub4e4\uc740 \uc5b8\uc81c\ub4e0 \ubb38\uc758\uc8fc\uc138\uc694.",
                "text_zh_cn": "\uc2a4\ud3f0 \ub9e4\uce6d \uc9c0\uc6d0 \uc11c\ube44\uc2a4\r\n\r\n\uc7a5\uae30\ub9cc\ub0a8(\uc2a4\ud3f0) \ub9e4\uce6d \uac00\ub2a5\ud569\ub2c8\ub2e4. \r\n\ucd5c\uc18c\uae30\uac04 \ud55c\ub2ec\ubd80\ud130 \uc2dc\uc791\ub429\ub2c8\ub2e4. \ub9cc\ub0a8 \uc774\ud6c4 \uc7a5\uae30 \ub9cc\ub0a8 \ud76c\ub9dd \ud558\uc2dc\ub294 \ubd84\ub4e4\uc740 \uc5b8\uc81c\ub4e0 \ubb38\uc758\uc8fc\uc138\uc694.",
                "text_en_us": "\uc2a4\ud3f0 \ub9e4\uce6d \uc9c0\uc6d0 \uc11c\ube44\uc2a4\r\n\r\n\uc7a5\uae30\ub9cc\ub0a8(\uc2a4\ud3f0) \ub9e4\uce6d \uac00\ub2a5\ud569\ub2c8\ub2e4. \r\n\ucd5c\uc18c\uae30\uac04 \ud55c\ub2ec\ubd80\ud130 \uc2dc\uc791\ub429\ub2c8\ub2e4. \ub9cc\ub0a8 \uc774\ud6c4 \uc7a5\uae30 \ub9cc\ub0a8 \ud76c\ub9dd \ud558\uc2dc\ub294 \ubd84\ub4e4\uc740 \uc5b8\uc81c\ub4e0 \ubb38\uc758\uc8fc\uc138\uc694.",
                "text_es_spa": "\uc2a4\ud3f0 \ub9e4\uce6d \uc9c0\uc6d0 \uc11c\ube44\uc2a4\r\n\r\n\uc7a5\uae30\ub9cc\ub0a8(\uc2a4\ud3f0) \ub9e4\uce6d \uac00\ub2a5\ud569\ub2c8\ub2e4. \r\n\ucd5c\uc18c\uae30\uac04 \ud55c\ub2ec\ubd80\ud130 \uc2dc\uc791\ub429\ub2c8\ub2e4. \ub9cc\ub0a8 \uc774\ud6c4 \uc7a5\uae30 \ub9cc\ub0a8 \ud76c\ub9dd \ud558\uc2dc\ub294 \ubd84\ub4e4\uc740 \uc5b8\uc81c\ub4e0 \ubb38\uc758\uc8fc\uc138\uc694.",
                "text_ms_my": "\uc2a4\ud3f0 \ub9e4\uce6d \uc9c0\uc6d0 \uc11c\ube44\uc2a4\r\n\r\n\uc7a5\uae30\ub9cc\ub0a8(\uc2a4\ud3f0) \ub9e4\uce6d \uac00\ub2a5\ud569\ub2c8\ub2e4. \r\n\ucd5c\uc18c\uae30\uac04 \ud55c\ub2ec\ubd80\ud130 \uc2dc\uc791\ub429\ub2c8\ub2e4. \ub9cc\ub0a8 \uc774\ud6c4 \uc7a5\uae30 \ub9cc\ub0a8 \ud76c\ub9dd \ud558\uc2dc\ub294 \ubd84\ub4e4\uc740 \uc5b8\uc81c\ub4e0 \ubb38\uc758\uc8fc\uc138\uc694.",
                "text_kor": null,
                "hot": 0,
                "create_time": "2023-08-13 18:59:06",
                "update_time": 1725804393
            }
        ],
      }
    };
  },
  created() {
    this.getNoticeList()
  },
  methods: {
    getNoticeList(){
      this.$http({
        method: 'get',
        url: 'sys_get_notice_list'
      }).then(res=>{
        this.notice = this.aaa.sdata
      })
    },
    back(){
      return window.history.back();
    },
    onRefresh() {
      setTimeout(() => {
        this.$toast(this.$t("reservation.refresh"));
        this.isLoading = false;
        this.getNoticeList();
      }, 500);
    },
  }
};
</script>

<style lang='less' scoped>
#app{
}
.van-nav-bar {
  height: 2.88rem;
  background: linear-gradient(20deg, #a253d0, #d63f8c);
}

.container{
  height: 100%;
  background: #f0f0f0;
}

.wrap{
  overflow-y: scroll;
  // height: calc(var(--vh) * 100 - 2.8125rem - 5%);
}
.card{
  width: 94%;
  margin: 0.63rem auto 0.31rem;
  border-radius: 0.50rem;
  background: #fff;
  padding: 0.63rem 1.00rem;
  .title{
    font-size: 1rem;
    color: #49328b;
  }
  .date{
    margin-top: 3%;
    margin-bottom: 3%;
    color: #999;
  }
  .message{
    font-size: .90625rem;
    color: #9a8fbf;
  }
}

</style>
