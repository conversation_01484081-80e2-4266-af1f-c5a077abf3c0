<template>
  <div class="container">
    <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('liushui.title') }}</span>
      </template>
    </van-nav-bar>
    
    <div class="wrap" id="content">
      <van-pull-refresh v-model="refresherStatus" @refresh="refresher" class="container" :pulling-text="$t('common.pullLabel1')" :loosing-text="$t('common.pullLabel2')" :loading-text="$t('common.pullLabel3')">
        <Nodata v-if="liushuiList.length === 0" />
        <van-list v-model="loading" :finished="finished" :finished-text="$t('common.nomore')" :loading-text="$t('common.loading')" :error-text="$t('common.error')" @load="onLoad">
          <div class="card" v-for="(item,index) in liushuiList" :key="index">
            <div class="left">
              <div class="title">
                <span>{{ item.typename }}</span>
                <span class="price">{{ changeMoney(item.money) }}KRW</span>
              </div>
              <div class="time">{{ item.created_at }}</div>
            </div>
            <div class="status">{{ item.remark }}</div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import { changeMoney } from "@/common/function";
import Nodata from "@/common/Nodata.vue";
import Nomore from "@/common/Nomore.vue";
export default {
  components: { Nodata, Nomore },
  data() {
    return {
      liushuiList:[],
      refresherStatus: '',
      params: {
        page: 0,
        limit: 20
      },
      liushuiData: {},
      loading: false,
      finished: false,
    };
  },
  created() {
  },
  methods: {
    getLiushui(refresh=true){
      this.params.page = refresh ? 1 : this.params.page
      return this.$http({
        method: 'get',
        url: 'liushui_list',
        data: this.params
      }).then(res=>{
        if(refresh) {
          this.liushuiList = res.data.data
        }else {
          this.liushuiList = [...this.liushuiList, ...res.data.data]
        }
        this.liushuiData = res.data
        this.loading = false
        if(this.params.page >= this.liushuiData.last_page) {
          this.finished = true
        }
      })
    },
    back(){
      return window.history.back();
    },

    onLoad() {
      this.params.page ++
      this.getLiushui(false)
    },
    
    async refresher() {
      this.refresherStatus = true
      await this.getLiushui()
      this.finished = false
      this.refresherStatus = false
    }
  }
};
</script>

<style lang='less' scoped>
.van-nav-bar {
  height: 2.88rem;
}

::v-deep .van-tabs {
  .van-tab, .van-tabs__wrap {
    height: 2.75rem;
  }
  .van-tab__text{
    font-size: 0.88rem;
    line-height: 2.75rem;
    color: #6f41b9;
  }
  .van-tab--active .van-tab__text{
    font-weight: bold;
  }
}

.container{
  height: 100%;
  background: #f0f0f0;
}

.wrap{
  .container {
    height: calc(var(--vh) * 100 - 2.88rem);
    overflow-y: auto;
  }
  .card{
    width: 100%;
    height: 4.13rem;
    padding: 0.63rem 1.00rem;
    border-radius: 0.50rem;
    background: #fff;
    padding: 0.63rem 1.00rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before{
      position: absolute;
      content: "";
      border: none;
      width: 90%;
      height: 0.06rem;
      background: linear-gradient(90deg, rgba(80, 59, 143, .15), rgba(80, 59, 143, .61), rgba(80, 59, 143, .15));
      bottom: 0;
    }
    .title {
      font-size: 0.88rem;
      color: #49328b;
    }
    .time{
      font-size: 0.75rem;
      color: #969799;
      margin-top: 0.25rem;
    }
    .label {
      font-size: 0.88rem;
      color: #49328b;
    }
    .status {
      font-size: 0.88rem;
      color: #969799;
    }
  }
}

::v-deep .van-list__finished-text{
  font-size: 1.00rem !important;
  height: 3.13rem;
  line-height: 3.13rem;
}
::v-deep .van-loading__text {
  font-size: 1.00rem !important;
}

</style>
