{"name": "kongjiang-front", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "i18n:report": "vue-cli-service i18n:report --src \"./src/**/*.?(js|vue)\" --locales \"./src/undefined/**/*.json\""}, "dependencies": {"animate.css": "^4.1.1", "axios": "^1.7.2", "core-js": "^3.8.3", "js-base64": "^3.7.7", "mu.js": "^1.1.2", "qs": "^6.12.3", "vant": "^2.12.27", "video.js": "^8.17.4", "videojs-contrib-hls": "^5.15.0", "vue": "^2.6.14", "vue-i18n": "^8.26.3", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "css-loader": "^6.2.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.1", "less-loader": "^7.3.0", "postcss-px-to-viewport": "^1.1.1", "style-loader": "^3.2.1", "swiper": "^6.3.5", "vue-awesome-swiper": "^3.1.3", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true}}]}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "jest": {"preset": "@vue/cli-plugin-unit-jest"}}