<template>
  <div class="container">
    
    <!-- <van-nav-bar>
      <template #title>
        <van-image src="img/home/<USER>" width="60"></van-image>
      </template>
    </van-nav-bar> -->
    <div class="head">
      <img src="/img/login/logo.png" alt="">
    </div>

    <div id="content">
      <div class="gonggao">
        <img src="/img/home/<USER>" alt="">
        <div class="scroll-container">
          <div class="scroll-text">{{ $t('index.text1') }}</div>
        </div>
      </div>
      <div class="banner">
        <swiper class="banner_swiper" :options="bannerSwiperOption">
          <swiper-slide v-for="(v, key) in banners" :key="key">
            <van-image class="banner_img" round :src="v.url">
              <template v-slot:loading>
                <van-loading type="circular" />
              </template>
            </van-image>
          </swiper-slide>
        </swiper>
      </div>

      <div class="card">
        <div class="top">
          <div class="left">
            <img src="/img/login/logo.png" alt="">
          </div>
          <div class="right">{{ $t('index.text2') }}</div>
        </div>
        <div class="bottom">{{ $t('index.text3') }}</div>
      </div>

      <div class="content">
        <div class="navList">
          <div class="nav" v-for="(item, index) in navList" :key="index" :class="{ active: navActive === index }" @click="navActive = index">{{ item }}</div>
        </div>
        <div class="xuanfeiList2" v-show="navActive === 0">
          <div class="row" v-for="item in xuanfeiList" :key="item.id" @click="goDetail(item)">
            <div class="left">
              <img :src="item.img_url[0]"></img>
            </div>

            <div class="right">
              <div class="name">
                <div class="label">{{ item.xuanfei_name }}</div>
                <div class="location">
                  <img src="/img/home/<USER>" alt="">
                  <span>{{ item.diqu }}</span>
                </div>
              </div>
              <div class="renzheng">
                <div>{{ $t('index.icon1') }}</div>
                <div>{{ $t('index.icon2') }}</div>
              </div>
              <div class="biaoqian">
                <div v-for="(biaoqian, index) in item.type.slice(0, 3)" :key="index">{{ biaoqian.name }}</div>
              </div>
              <div class="xingji">
                <div>
                  <span>{{ $t('index.label1') }}：</span>
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                </div>
              </div>
              <div class="button2">{{ $t('index.button') }}</div>
            </div>
          </div>
        </div>
        <div class="plCard" v-show="navActive === 1">
          <div class="title">{{  $t('index.text4') }}{{ randomNum }}{{  $t('index.text5') }}</div>
          <swiper class="pl_swiper"  :options="plOption">
            <swiper-slide style="text-align: center; color: #fff;height: 1.5rem;line-height: 1.5rem;" v-for="(v, key) in plList" :key="key">{{ v }}</swiper-slide>
          </swiper>
        </div>
        <div class="tiaokuan" v-show="navActive === 2">
          {{ $t('index.tiaokuan') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      banners: [],
      navActive: 0,
      navList: [ this.$t('index.nav1'), this.$t('index.nav2'), this.$t('index.nav3') ],
      tuis: [
        {
          id: 1,
          num: '001',
          name: '',
          pic: require("@/assets/imgs/index_pic/1.jpg"),
          avatar: require("@/assets/imgs/index_avatar/11.jpg"),
          sui: '22세/165cm/46kg'
        },
        {
          id: 2,
          num: '002',
          name: '',
          pic: require("@/assets/imgs/index_pic/2.jpg"),
          avatar: require("@/assets/imgs/index_avatar/12.jpg"),
          sui: '26세/170cm/53kg'
        },
        {
          id: 3,
          num: '003',
          name: '',
          pic: require("@/assets/imgs/index_pic/3.jpg"),
          avatar: require("@/assets/imgs/index_avatar/13.jpg"),
          sui: '21세/169cm/51kg'
        },
        {
          id: 4,
          num: '004',
          name: '',
          pic: require("@/assets/imgs/index_pic/4.jpg"),
          avatar: require("@/assets/imgs/index_avatar/14.jpg"),
          sui: '23세/165cm/49kg'
        },
        {
          id: 5,
          num: '005',
          name: '',
          pic: require("@/assets/imgs/index_pic/5.jpg"),
          avatar: require("@/assets/imgs/index_avatar/15.jpg"),
          sui: '25세/167cm/50kg'
        },
        {
          id: 6,
          num: '006',
          name: '',
          pic: require("@/assets/imgs/index_pic/6.jpg"),
          avatar: require("@/assets/imgs/index_avatar/16.jpg"),
          sui: '22세/162cm/47kg'
        },
        {
          id: 7,
          num: '007',
          name: '',
          pic: require("@/assets/imgs/index_pic/7.jpg"),
          avatar: require("@/assets/imgs/index_avatar/17.jpg"),
          sui: '34세/170cm/54kg'
        },
        {
          id: 8,
          num: '008',
          name: '',
          pic: require("@/assets/imgs/index_pic/8.jpg"),
          avatar: require("@/assets/imgs/index_avatar/18.jpg"),
          sui: '30세/166cm/49kg'
        },
        {
          id: 9,
          num: '009',
          name: '',
          pic: require("@/assets/imgs/index_pic/9.jpg"),
          avatar: require("@/assets/imgs/index_avatar/19.jpg"),
          sui: '24세/172cm/52kg'
        },
        {
          id: 10,
          num: '010',
          name: '',
          pic: require("@/assets/imgs/index_pic/10.jpg"),
          avatar: require("@/assets/imgs/index_avatar/20.jpg"),
          sui: '22세/168cm/49kg'
        }
      ],
      bannerSwiperOption: {
        autoplay: true,
      },
      xuanfeiList: [],
      gameList: [],
      plList: [
        'hf*****jfq   约炮账号激活成功',
        '8k***gf 约炮账号入会成功',
        'm***ztcw 预约***93美女成功',
        'dv***qu 约炮账号激活成功',
        'dh***dxg 预约***24美女成功',
        'b5***dy5 预约***75美女成功',
        'er***gkz  约炮账号激活成功',
        '2t***8ug   约炮账号激活成功',
        'f2*****fhg  约炮账号激活成功',
        'a3***9pt 约炮账号激活成功',
        'nw***n64约炮账号入会成功',
        'xx***cm  约炮账号入会成功',
        'sp***zdd 约炮账号入会成功',
        '5h***j3cx  约炮账号激活成功',
        'xy***eet 约炮账号激活成功',
        'q9***mbf 预约***52美女成功',
        'c4***vc 预约***86美女成功',
        '24j***wqh 预约***10美女成功',
        'qj***2xt 约炮账号入会成功',
        '6jt***h2 预约**06美女成功',
        'hf*****jfq   约炮账号激活成功',
        '8k***gf 约炮账号入会成功',
        'm***ztcw 预约***93美女成功',
        'dv***qu 约炮账号激活成功',
        'dh***dxg 预约***24美女成功',
        'b5***dy5 预约***75美女成功',
        'er***gkz  约炮账号激活成功',
        '2t***8ug   约炮账号激活成功',
        'f2*****fhg  约炮账号激活成功',
        'a3***9pt 约炮账号激活成功'
      ],
      plOption: {
        autoplay: {
          delay: 1500, // 每次滚动间隔时间，单位毫秒 (3000ms = 3秒)
          disableOnInteraction: false // 用户交互后不停止自动播放
        },
        direction: 'vertical',
        slidesPerView: 5,
        speed: 800,
      }
    }
  },
  computed: {
    swipeWidth() {
      return window.innerWidth * 0.45
    },
    randomNum() {
      return parseInt(Math.random() * 1000) + 1000
    }
  },
  methods: {
    getBasicConfig() {
      this.$http({
        method: 'get',
        url: 'sys_config'
      }).then(res => {
        this.banners = res.data?.banners || [];
      })

    },

    getXuanfeiList() {
      this.$http({
        method: 'get',
        url: 'xuanfei_list'
      }).then(res => {
        if (res.code === 200) {
          this.xuanfeiList = res.data.data.map(item => {
            item.info = `신장：${item.height}cm 사이즈：${item.bust} 서비스가능지역：${item.diqu}`
            return item
          })
        } else if (res.code === 401) {
          this.$toast(res.msg);
        }
      })
    },

    goDetail(e) {
      localStorage.setItem('xuanfeiDetail', JSON.stringify(e))
      this.$router.push(`XuanfeiDetail`);
    },

    goGame(e) {
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        this.$router.push({path:'/WindowLottery?key='+e.key+"&id="+e.id})
      }
    },

    
    getGameItem(){
      this.$http({
        method: 'get',
        url: 'lottery_list'
      }).then(res=>{
        this.gameList = res.data;
      })
    },
  },
  created() {
    this.getBasicConfig();
    this.getGameItem()
    this.getXuanfeiList()
  }
}
</script>


<style lang="less" scoped>
.container {
  background: url('/public/img/bg.png') repeat top / 100% auto;
  .head {
    display: flex;
    justify-content: center;
    height: 4.38rem;
    align-items: center;
    img {  
      width: 3.75rem;
      height: 3.75rem;
    }
  }

  .card {
    background: repeating-linear-gradient(-60deg,#f85c9a,#f64a8e,#f85c9a,#f64a8e,#f85c9a,#f64a8e,#f85c9a,#f64a8e);
    height: 12.63rem;
    margin: .66667rem auto;
    border-radius: .66667rem;
    padding: .66667rem;
    .top {
      display: flex;
      align-items: center;
      .left {
        width: 9.50rem;
        height: 7.50rem;
        border-right: 0.06rem solid #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 6.75rem;
          height: 6.75rem;
        }
      }
      .right {
        flex: 1;
        padding-left: 1.13rem;
        color: #fff;
        font-size: 0.88rem;
      }
    }
    .bottom {
      color: #fff;
      padding: 0.88rem 1.13rem 0;
      font-size: 0.88rem;
    }
  }

  .content {
    background: url('/public/img/home/<USER>') no-repeat top/100%;
    overflow: hidden;
    position: relative;
    padding: 0 2%;
    border-radius: .67rem;
    margin-bottom: 1rem;
    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      background: #fe6691;
      left: 0;
      bottom: -7.33333rem;
      z-index: 0;
    }
    .navList {
      margin-top: 8.13rem;
      margin-bottom: 0.75rem;
      position: relative;
      display: flex;
      height: 2.13rem;
      .nav {
        color: #ea4d4d;
        background: #fff;
        padding: .33333rem 1rem;
        margin-right: .33333rem;
        border-radius: 3.26667rem;
      }
      .active {
        background: linear-gradient(180deg, #fff2ef, #fadad7, #f7cac8);
      }
    }
    .xuanfeiList2 {
      position: relative;
      .row {
        overflow: hidden;
        border: 4PX solid #fff;
        border-radius: .6rem .6rem .66667rem .66667rem;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        min-height: 11.66667rem;
        background: #fff;
        margin-bottom: .66667rem;
        height: 13.38rem;
        .left {
          width: 40%;
          img {
            height: 12.88rem;
            width: 100%;
            object-fit: cover;
            border-radius: 0.4rem 0 0 0.4rem;
          }
        }
        .right {
          flex: 1;
          padding-left: 0.67rem;
          max-width: calc(100% - 7.67rem);
          padding: 0.5rem;
          .name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.38rem 0.38rem .8rem;
            .label {
              color: #000;
              font-size: 1rem;
            }
            .location {
              gap: 0.31rem;
              display: flex;
              align-items: center;
              img {
                width: 1rem;
                object-fit: contain;
                display: block;
              }
              color: #000;
              font-size: 1rem;
            }
          }
          .renzheng {
            display: flex;

            >div {
              background: linear-gradient(180deg, #e7caaf, #fff7ed);
              color: #a4826b;
              font-weight: 600;
              margin-left: .2rem;
              padding: .16rem .4rem;
              font-size: .73333rem;
              border-radius: .46667rem 0 .46667rem 0;
              white-space: nowrap;
              display: flex;
              align-items: center;

              &::before {
                content: '';
                width: .8125rem;
                height: .8125rem;
                background: url('/public/img/home/<USER>');
                background-size: 100% 100%;
              }
            }
          }

          .biaoqian {
            display: flex;
            flex-wrap: wrap;
            padding: 0.3rem 0;

            div {
              display: flex;
              justify-content: center;
              align-items: center;
              padding: .2rem .4rem;
              margin: .16667rem .16667rem;
              color: #fff;
              border-radius: .33333rem;
              font-size: .76667rem;

              &:nth-child(1n) {
                background-color: #fe257f;
              }

              &:nth-child(2n) {
                background-color: #ff9702;
              }

              &:nth-child(3n) {
                background-color: #0fa7fe;
              }
            }
          }

          .xingji {
            color: #f4b757;
            font-size: .9rem;
            padding-bottom: .3rem;
          }

          .info {
            overflow: hidden;

            >div {

              font-size: .9rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: #ccc;
            }
          }

          .button2 {
            text-align: center;
            background: linear-gradient(90deg,#fa6ba6,#f32879);
            color: #fff;
            border-radius: 1rem;
            padding: .33333rem 0;
            margin: .33333rem 0 .66667rem;
          }
        }
      }
    }
    .xuanfeiList {
      position: relative;
    }
  }
}

#content{
  padding: 0 3% 3.2rem;
}


.banner_swiper {
  height: 100%;
  width: 100%;

  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 11.25rem;
    text-align: center;
    font-weight: bold;
    font-size: 0.625rem;
    background-color: #ffffff;
    background-position: center;
    background-size: cover;
    color: #ffffff;
  }

  ::v-deep .swiper-container-3d .swiper-slide-shadow-left {
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
  }

  ::v-deep .swiper-container-3d .swiper-slide-shadow-right {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
  }

  .banner_img {
    border-radius: 0;
    width: 100%;
    height: 100%;
  }
}

.content {
  margin: 0 auto;

  .txt {
    font-size: .9375rem;
    color: #000;
    margin-top: 5%;
    margin-bottom: 5%;
    .icon{
      display: inline-block;
      margin-left: .46667rem;
      border-radius: .46667rem 0 .46667rem 0;
      background: #3f3a5b;
      color: #ebcaaf;
      padding: .04rem .6rem;
      font-size: 0.88rem;
    }
  }


  .swiper_wrap {
    overflow-x: auto;

    .swiper {
      display: flex;

      .swipe {
        width: 8.67rem;
        height: 8.67rem;
        overflow: hidden;
        margin-right: .67rem;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        border-radius: .33333rem;

        .tip {

          position: absolute;
          z-index: 9;
          background-color: #ebcaaf;
          color: #8d684b;
          width: 6.8rem;
          height: 6.8rem;
          transform: rotate(45deg);
          left: 5.66667rem;
          top: -4rem;

          span {

            position: absolute;
            bottom: 0;
            left: 1.6rem;
            font-size: .76667rem;
            font-weight: 600;
          }
        }
      }
    }
  }

  .xuanfeiList {
    .row {
      display: flex;
      width: 100%;
      border-bottom: 0.06rem solid #eee;
      padding-bottom: .2rem;
      margin-bottom: 0.67rem;
      align-items: center;

      .left {
        width: 7rem;
        text-align: center;
        color: #000;
        position: relative;
        overflow: hidden;
        border-radius: .4rem;

        ::v-deep .van-image__img {
          border-radius: .4rem;
        }

        .tip {
          position: absolute;
          transform: rotate(45deg);
          right: -5.66667rem;
          width: 6.66667rem;
          height: 6.66667rem;
          top: -3.33333rem;
          background: #ebcaaf;
          color: #8d684b;
          opacity: .9;

          span {
            position: absolute;
            font-weight: 600;
            left: .8rem;
            bottom: 0;
            font-size: .8rem;
          }
        }
      }

      .right {
        flex: 1;
        padding-left: 0.67rem;
        max-width: calc(100% - 7.67rem);

        .renzheng {
          display: flex;

          >div {
            background: linear-gradient(180deg, #e7caaf, #fff7ed);
            color: #a4826b;
            font-weight: 600;
            margin-left: .2rem;
            padding: .16rem .4rem;
            font-size: .73333rem;
            border-radius: .46667rem 0 .46667rem 0;
            white-space: nowrap;
            display: flex;
            align-items: center;

            &::before {
              content: '';
              width: .8125rem;
              height: .8125rem;
              background: url('/public/img/home/<USER>');
              background-size: 100% 100%;
            }
          }
        }

        .biaoqian {
          display: flex;
          flex-wrap: wrap;
          padding: 0.3rem 0;

          div {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .2rem .4rem;
            margin: .16667rem .16667rem;
            color: #fff;
            border-radius: .33333rem;
            font-size: .76667rem;

            &:nth-child(1n) {
              background-color: #fe257f;
            }

            &:nth-child(2n) {
              background-color: #ff9702;
            }

            &:nth-child(3n) {
              background-color: #0fa7fe;
            }
          }
        }

        .xingji {
          color: #f4b757;
          font-size: .9rem;
          padding-bottom: .3rem;
        }

        .info {
          overflow: hidden;

          >div {

            font-size: .9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #ccc;
          }
        }

        .button2 {
          text-align: center;
          background: linear-gradient(90deg, #df35ad, #4f1db5);
          color: #fff;
          border-radius: .26667rem;
          padding: .33333rem 0;
          width: 90%;
          margin: .33333rem 0 .66667rem;
          max-width: 20rem;
        }
      }
    }
  }

  .ganeGroup{
    display: flex;
    .gameItem{
      flex: 1;
      text-align: center;
    }
    .image{
      width: 70%;
      height: 6rem;
      margin: 0 auto;
    }
    .label{
      font-size: 1rem;
      padding-top: .7rem;
      color: #34196a;
    }
    .van-image{
      width: 100%;
      height: 100%;
    }
  }

  .plCard{
    width: 96%;
    margin: 0 auto 1.25rem;
    // height: 11.25rem;
    padding: 0.63rem 1.25rem;
    border-radius: 0.94rem;
    position: relative;
    .title{
      color: #fff;
      font-size: 1.13rem;
      font-weight: bold;
      text-align: center;
      margin-bottom: 1.63rem;
    }
    .pl_swiper{
      height: 11.5rem;
    }
  }
  .tiaokuan {
    position: relative;
    color: #fff;
    padding: 1rem 0.5rem 1.5rem;
  }
}

.gonggao {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 2rem;
  padding: 0.5rem 1rem;
  margin-bottom: .5rem;

  img {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    flex-shrink: 0;
  }

  .scroll-container {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    height: 1.5rem;
    line-height: 1.5rem;
  }

  .scroll-text {
    display: inline-block;
    animation: scroll-left 10s linear infinite;
    color: #333;
    font-size: 0.875rem;
  }
}

@keyframes scroll-left {
  0% {
    transform: translateX(200%);
  }
  100% {
    transform: translateX(-100%);
  }
}
</style>