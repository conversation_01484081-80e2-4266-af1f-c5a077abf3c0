<template>
  <div class="container">
    <van-nav-bar :title="$t('withdraw.withdraw')">
      <template #left>
        <van-icon name="arrow-left" color="#fff" size="20"  @click="back()"></van-icon>
      </template>
      <template #right>
        <div class="record" @click="goRecord">{{ $t('withdraw.record') }}</div>
      </template>
    </van-nav-bar>
    <div class="header">
      <div class="ipu">
        <div class="title">{{ $t("withdraw.withdraw_balance") }}</div>
        <div class="input_wrap">
          <van-field v-model="withdraw_money" :placeholder="$t('withdraw.placeholder')" type="number" />
          <div class="hs">
            <div class="line"></div>
            <div class="label">{{ $t("recharge.currency") }}</div>
          </div>
        </div>
        <div class="balance">{{ $t("withdraw.amount_balance") }}: <span>{{ changeMoney(userInfo.money) }} {{ $t("recharge.currency") }}</span></div>
      </div>
      
      <div class="ipu">
        <div class="text">{{ $t("withdraw.method") }}</div>
        <div class="label line">{{ $t("withdraw.bank_account") }}</div>
        <div class="label">{{ $t("withdraw.bank_name") }}</div>
        <div class="label">{{ $t("withdraw.back_account") }}</div>
        <div class="label line">{{ $t("withdraw.name") }}</div>
        <div class="text" style="margin-top: 5%;">{{ $t("withdraw.send_password") }}</div>
        <div class="input_wrap input_wrap2">
          <van-field v-model="pay_password" :placeholder="$t('withdraw.pay_placeholder')" type="password" clearable/>
        </div>
      </div>

      <div class="button" @click="submit">{{ $t("withdraw.submit") }}</div>
    </div>
  </div>
</template>

<script>
import { changeMoney } from '@/common/function';

let Base64 = require('js-base64').Base64;
export default {
  data() {
    return {
      userInfo: {},
      withdraw_money: null,
      pay_password: null,
      modal: false
    };
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
    }
    
  },
  methods: {
    back(){
      return window.history.back();
    },
    goRecord() {
      this.$router.push("WithdrawRecord");
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        console.log(res)
        if(res.code === 200){
            this.userInfo = res.data;
            if(this.userInfo.status !== 1){
              this.$toast(this.$t("video.account_out"));
              localStorage.clear()
              this.$router.push({path:'/Login'})
            }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    showMoadl() {
      if(this.withdraw_money <= 0){
        this.$toast(this.$t("setting.correct_money"));
        return false;
      }else {
        this.modal = true
      }
    },
    submit() {
      if(!this.pay_password){
        this.$toast(this.$t("withdraw.correct_pay_password"));
        return false;
      }else {
        this.$http({
          method: 'post',
          data:{money:this.withdraw_money, pay_password: this.pay_password},
          url: 'user_set_withdraw'
        }).then(res=>{
          this.$toast(res.msg);
          if(res.code === 200){
            this.getUserInfo();
            this.modal = false
            this.pay_password = null
            this.withdraw_money = null
          }
        })
      }
    },
  }
};
</script>

<style lang='less' scoped>
.container{
  background: url("/public/img/user/withdrawBg.png") no-repeat top / 100%, #fff;
}

.van-nav-bar {
  background: none;
  height: 2.88rem;
  position: relative !important;

  /deep/ .van-nav-bar__title{
    color: #fff;
    font-size: 1rem;
  }
  /deep/ .van-nav-bar__left{
    padding: 0 1rem;
  }
  .record{
    font-size: 0.88rem;
    color: #fff;
  }
}


.ipu{
  width: 95%;
  margin: 0.75rem auto 0;
  // height: 8rem;
  background: #fff;
  border-radius: 0.5rem;
  padding: 1rem 5%;
  box-shadow:#ccc 0.00rem 0.00rem 0.22rem;
  .title{
    font-size: .9rem;
    color: #442b89;
  }
  .input_wrap{
    background-color: inherit !important;
    background: #321b51;
    border-radius: 6.25rem;
    border: 0.06rem solid #ccc4cf;
    height: 3.00rem;
    margin-top: 4.5%;
    margin-bottom: 4.5%;
    display: flex;
    align-items: center;
    .hs{
      display: flex;
      align-items: center;
      width: 20%;
      justify-content: flex-start;
      .line{
        width: .0625rem;
        background-color: #999;
        height: 1rem;
        margin-left: 10%;
        margin-right: 10%;
      }
      .label{
        color: #442b89;
        font-size: 1rem;
      }
    }
  }

  
  
  .van-field{
    background-color: transparent;
    padding: 0 1.25rem;
    color: #fff;
    &::-webkit-input-placeholder {
      color: #323233;
    }
    font-size: 0.88rem;
    display: flex;
    align-items: center;
    border-bottom: 0 !important;
    border: none;
    height: 1.50rem;
    &::after{
      display: none;
    }
    /deep/ .van-field__control{
      color: #323233;
      background-color: transparent;
      font-size: .88rem;
    }

    /deep/ .van-field__body{
      height: 1.50rem;
    }
    
  }
  .balance{
    margin-top: 5%;
    color: #3a1a84;
    font-size: .9rem;
    span{
      color: #d53dde;
    }
  }
  
  .input_wrap2{
    .van-field{
      width: 100%;
    }
  }

  .label{
    font-size: 1rem;
    margin-top: 5%;
    padding-bottom: 5%;
    color: #442b89;
  }
  .text{
    color: #442b89;
  }
  .line{
    border-bottom: 0.06rem solid #eee;
  }
}

.button{
  width: 95%;
  margin: 10% auto;
  background: linear-gradient(90deg,#e9557f,#7e55e9);
  border-radius: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  padding: 1.4rem 0;
}

</style>
