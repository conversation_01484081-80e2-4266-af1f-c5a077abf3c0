<template>
  <div class="container">
    <van-nav-bar :title="$t('recharge.recharge')">
      <template #left>
        <van-icon name="arrow-left" color="#fff" size="20"  @click="back()"></van-icon>
      </template>
      <template #right>
        <div class="record" @click="goRecord">{{ $t('recharge.record') }}</div>
      </template>
    </van-nav-bar>
    <div class="header" id="content">
      <div class="yue">
        <p class="title">{{$t("recharge.curr_balance")}}</p>
        <p class="value">{{ userInfo.money }}</p>
      </div>
      <div class="ipu">
        <div class="title">{{ $t("recharge.recharge_balance") }}</div>
        <div class="input_wrap">
          <van-field v-model="recharge_balance" :placeholder="$t('recharge.input_money')" type="number" />
          <div class="hs">
            <div class="line"></div>
            <div class="label">{{ $t("recharge.currency") }}</div>
          </div>
        </div>
      </div>
      <div class="button" @click="submit">{{ $t("recharge.submit") }}</div>

      <div class="text">
        <div class="row">{{ $t("recharge.row1") }}</div>
        <div class="row">{{ $t("recharge.row2") }}</div>
        <div class="row">{{ $t("recharge.row3") }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {},
      recharge_balance: null
    };
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
    }
    
  },
  methods: {
    back(){
      return window.history.back();
    },
    goRecord() {
      this.$router.push("ChongRecord");
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
            this.userInfo = res.data;
            if(this.userInfo.status !== 1){
              this.$toast(this.$t("video.account_out"));
              localStorage.clear()
              this.$router.push({path:'/Login'})
            }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    submit() {
      if(!this.recharge_balance) {
        return
      }
      this.$http({
        url: "userRecharge",
        method: "post",
        data: { 
          amount: this.recharge_balance,
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$toast.success(res.msg);
          // localStorage.setItem("token", res.data.id);
        } else {
          this.$toast(res.msg);
        }
      });
    },
  }
};
</script>

<style lang='less' scoped>
.container{
  background: url("/public/img/user/withdrawBg.png") no-repeat top / 100%, #fff;
}

.van-nav-bar {
  background: none;
  height: 2.88rem;

  /deep/ .van-nav-bar__title{
    color: #fff;
    font-size: 1rem;
  }
  /deep/ .van-nav-bar__left{
    padding: 0 1rem;
  }
  .record{
    font-size: 0.88rem;
    color: #fff;
  }
}

.header{
  .yue{
    background-image: url("/public/img/mine/yue.png");
    background-size: 100% 100%;
    height: 7rem;
    width: 95%;
    margin: 5% auto 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1.4rem 1.2rem;
    .title{
      color: #fff;
      font-size: 1rem;
      margin: 0;
    }
    .value{
      color: #fff;
      font-size: 1.62rem;
      margin: 0;
    }
  }
  .ipu{
    width: 95%;
    margin: 0.75rem auto 0;
    // height: 8rem;
    background: #fff;
    border-radius: 0.5rem;
    padding: 1rem 5%;
    box-shadow:#ccc 0.00rem 0.00rem 0.22rem;
    .title{
      font-size: .9rem;
      color: #442b89;
    }
    .input_wrap{
      background-color: inherit !important;
      background: #321b51;
      border-radius: 6.25rem;
      border: 0.06rem solid #ccc4cf;
      height: 3.00rem;
      margin-top: 4.5%;
      margin-bottom: 4.5%;
      display: flex;
      align-items: center;
      .hs{
        display: flex;
        align-items: center;
        width: 20%;
        justify-content: flex-start;
        .line{
          width: .0625rem;
          background-color: #999;
          height: 1rem;
          margin-left: 10%;
          margin-right: 10%;
        }
        .label{
          color: #442b89;
          font-size: 1rem;
        }
      }
    }
  }
  .button{
    width: 95%;
    margin: 0 auto;
    background: linear-gradient(90deg,#e9557f,#7e55e9);
    border-radius: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    padding: 1.4rem 0;
    margin-top: 15%;
  }
  .text{
    width: 95%;
    margin: 0 auto;
    margin-top: 8%;
    color: #442b89;
    // opacity: .5;
    font-size: .9375rem;
    .row{
      margin-bottom: 5%;
    }
  }
  
  .van-field{
    background-color: transparent;
    padding: 0 1.25rem;
    color: #fff;
    &::-webkit-input-placeholder {
      color: #323233;
    }
    font-size: 0.88rem;
    display: flex;
    align-items: center;
    border-bottom: 0 !important;
    border: none;
    height: 1.50rem;
    &::after{
      display: none;
    }
    /deep/ .van-field__control{
      color: #323233;
      background-color: transparent;
      font-size: .88rem;
    }

    /deep/ .van-field__body{
      height: 1.50rem;
    }
    
  }
}

</style>
