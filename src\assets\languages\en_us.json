{"common": {"pullLabel1": "Pull to refresh...", "pullLabel2": "Refresh...", "pullLabel3": "Loading...", "nodata": "No data yet", "nomore": "No exchange history", "loading": "Loading...", "error": "Loading failed"}, "tabbar": {"index": "Home", "video": "Video Gallery", "goods": "Profile", "message": "Reviews", "user": "My Account"}, "index": {"tip1": "Available nationwide", "tip2": "Advanced", "title1": "Enjoy the ultimate luxury experience", "title2": "Activate window", "title3": "Popular Recommended List", "icon1": "Actual verification", "icon2": "Own video verification", "label1": "Rating", "label2": "Height", "label3": "Size", "label4": "Available service area", "button": "Apply for a date"}, "xuanfeiDetail": {"label1": "Real-life verification", "label2": "Self-video verification", "label3": "Height", "label4": "Size", "label5": "Rating", "title": "Girl profile", "title2": "Detailed information", "title3": "Live video", "title4": "Security policy", "button": "Click to verify", "text": "Note: Intimacy can only be registered through customer service for verification or successful matching to ensure the protection of personal information of users throughout the entire network."}, "videoDetail": {"count": "<PERSON>soo", "title": "Popular recommendation"}, "goods": {"title": "Best night", "count": "<PERSON>soo", "subscribe": "Matching application", "every": "every"}, "user": {"label": "Credit score", "balance": "Points held", "recharge": "Point recharge", "withdraw": "Point withdrawal", "unit": "KRW", "logout": "Log out", "icon1": "Basic information", "icon2": "Login password", "icon3": "Withdrawal history", "icon4": "Gift history", "icon5": "Notice", "icon6": "Viewing history", "icon7": "Use password", "icon8": "Point use history", "process": "Accumulated points"}, "MyMove": {"title": "my movie", "info": "Complete tasks to get more movie tickets", "btn": "Film reel", "label": "Watchlist"}, "userInfo": {"title": "Basic information", "bank_id": "ID", "bank_name": "Bank name", "user_name": "Depositor name", "bank_account": "Account number", "icon6": "Viewing history"}, "setting": {"setting": "Settings", "base_setting": "Basic information", "login_pwd": "Login password", "finance_pwd": "Fund password", "language": "Select language", "logout": "Logout", "button": "Change", "avatar": "Incarnation", "choose_avatar": "Select an avatar", "ok": "Confirm", "cancel": "Cancel", "real_name": "Real name", "modify_real_name": "Change real name", "name_place": "Please enter your real name", "save": "Save", "save_password": "Submit", "name": "Name", "name_tip": "For the security of your account, your real name must match the name on your bank card.", "repect": "Do not repeat the settings!", "sex": "Gender", "sex_place": "Edit gender", "man": "Male", "female": "Female", "unkown": "Unknown", "no_setting": "Not set", "y_setting": "Set", "bindinged": "Border", "no": "None", "login_pwd_tip": "Change login password", "old_pwd": "Enter your old password", "new_pwd": "Enter your new password", "new_again": "Re-enter your new password", "old_pwd_tip": "Enter your old password", "new_pwd_tip": "Enter your new password", "new_again_tip": "Please re-enter your new password", "prefect": "Please fill it out perfectly!", "pwd_error": "The two passwords you entered do not match", "set_money_pwd": "Set fund password", "money_place": "Please enter your fund password", "money_again_place": "Please enter your fund password", "correct_money": "Please enter the correct amount", "contact_recharge": "If you want to recharge, please contact customer service", "set_bank": "Please set up a payment card", "forbid": "The function has been disabled.", "log_reg": "Log in Sign up", "more_service": "You can enjoy more services by logging in!", "bind_bank_info": "Bind bank card information", "bank_info": "Bank card information", "add_bank": "Add bank card", "bind_bank_tip": "Tips: Please bind to a large commercial bank. If you need to modify, please contact online customer service.", "fill_bank": "Withdraw points", "fill_bank_tip": "Please enter your payment card information", "band_account": "Bank account number", "set_name_bank": "Please set your name before binding your bank card!", "set_pwd_bank": "Please set your withdrawal password before binding your bank card!", "band_name": "Bank name", "band_account_tip": "Enter your real bank card number.", "band_name_tip": "Enter the cardholder name.", "bank_warn": "Customer, for the security of your funds, please set your real name and set your withdrawal password. If it does not match the account opening name, withdrawal is not possible.", "bank_ok": "Confirm card binding", "username": "Depositor name", "username_place": "Please enter your real name", "mobile": "Phone number", "mobile_place": "Please enter your phone number", "password": "Enter your password", "password_tip": "Enter your cash withdrawal password.", "password_tip2": "Withdrawal password can only be 4 digits long", "password_tip3": "Enter your password confirmation.", "password_tip4": "The two passwords are different", "password2": "Confirm password"}, "liushui": {"title": "Exchange record"}, "gameRecord": {"title": "Work History", "type1": "All", "type2": "Bonus Points", "type3": "Pending Results", "type4": "Mission Amount History", "label1": "Order Contents", "label2": "Day", "label3": "Period"}, "gameRecordDetail": {"title": "Mission Results", "unit": "Points", "label1": "Task Points", "label2": "Order Contents", "label3": "Order Time", "label4": "Details", "column1": "Contents", "column2": "Amount", "column3": "Results", "column4": "Time", "status": "Gift Completed", "status1": "Results Waiting", "status2": "Mission Failed", "status3": "Victory"}, "message": {"title": "Manager Review"}, "messageDetail": {"label": "Author", "title": "Comment Details"}, "language": {"title": "Language correction"}, "recharge": {"title": "charge", "label": "Points held", "recharge": "Recharge", "curr_balance": "Current balance", "input_money": "Enter the recharge amount", "pay_way": "Select a payment method", "record": "Record", "recharge_balance": "Recharge amount", "currency": "KRW", "submit": "Submit", "row1": "1. Do not submit the recharge amount repeatedly", "row2": "2. The actual deposit amount and the amount requested for recharge must match.", "row3": "3. If the recharge is not completed normally, please contact the customer center.", "recharge_record": "Recharge record", "create_time": "Submission time", "amount": "Amount", "info": "If you want to recharge offline, please contact the customer center. The recharge amount for some payment channels is randomly recharged up to the first decimal place, so please check when making a payment.", "button": "Contact Customer Service"}, "auth": {"login": "<PERSON><PERSON>", "username_place": "Enter your username", "pwd_place": "Enter your password", "forgetpwd": "Forgot your password", "no_account": "Don't have an account? Sign up now", "register": "Register", "invite_code_place": "Enter your invitation code", "agreement_place": "I understand and agree to the terms of the account opening agreement.", "agreement": "Please check the account opening agreement below!"}, "foorter": {"index": "Homepage", "subscribe": "Reservation", "video": "video", "my": "MYpage", "game_record": "Game history", "logo": "Select manager"}, "my": {"title": "xxxxxx", "recharge": "Recharge", "withdraw": "<PERSON><PERSON><PERSON>", "liushui": "Gift history", "my_balance": "My wallet", "detail": "Details", "balance": "Balance", "balance2": "Balance", "my_statement": "Personal report", "account_detail": "Account details", "task_record": "Betting history", "personal_center": "Personal center", "information_announcement": "Announcements", "online_service": "Online service", "finish_task": "Please complete the task list before entering.", "contact": "Connect", "service_time": "Dedicated service for you 7*24 hours a day", "sys_notice": "System notifications", "set_password": "Change password", "sign_out": "Logout", "big": "Big", "small": "Small", "double": "Even", "single": "Odd"}, "reservation": {"hall": "Reservation Hall", "refresh": "Refresh Success", "money_err": "Incorrect Amount", "choose_num": "Please select a number!", "balance_enough": "If your balance is insufficient, please contact the customer center to recharge!", "contact_admin": "Please contact the administrator to receive this task.!", "prize_succ": "The lottery draw was successful. Issue number:", "task_list": "Reservation", "available_balance": "Remaining points", "counselor": "Please contact the counselor or reception.", "clear_order": "Clear order", "submit": "Reservation", "num": "Problem", "win_num": "Winning number", "curr_choose": "Current selection", "per_price": "Betting points", "price_place": "Please enter the amount.", "unit": "Won", "total": "Total", "note": "Memo", "money": "Betting amount", "order_time": "Registration time", "settle_time": "Aggregation time", "no_choose": "Deselected", "big": "Big", "small": "Small", "double": "Double", "single": "One", "win_formula": "Profit calculation formula: Winning amount - Task amount", "type": "Game history"}, "withdraw": {"with_center": "Withdrawal center", "with_record": "Withdrawal record", "with_money": "Withdrawal amount", "recharge_money": "Recharge amount", "task_money": "Task amount", "win_money": "Winning amount", "single_limit": "Single transaction limit", "low": "Lowest", "heigh": "Highest", "with_num": "Withdrawal number: Maximum withdrawal number per day", "number": "Secondary", "with_tip": "Arrival time: Normal arrival time is about 5 minutes, fastest arrival time is about 2 minutes.", "limit_desc": "Limitation description", "immediately_withdraw": "Withdraw money immediately", "empty_data": "Data is empty. Amount", "money": "Describe", "desc": "Submission time", "submit_time": "Review time", "check_time": "Withdrawal password is set. If you need to modify it, please contact the customer center.", "with_service": "Cash withdrawal", "withdraw": "Record", "record": "Please enter the withdrawal amount", "placeholder": "Cash withdrawal amount", "withdraw_balance": "Point withdrawal", "pay_password": "Please enter the withdrawal password", "pay_placeholder": "Please enter the cash withdrawal password", "correct_pay_password": "Account balance", "amount_balance": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "create_time": "Submission time", "update_time": "Completion time", "method": "Cash withdrawal method", "bank_account": "Bank account", "bank_name": "Bank name", "back_account": "Refund account number", "name": "Name", "send_password": "Enter withdrawal password"}, "video": {"video": "Video Cinema", "play": "Play", "no_more": "No more", "num_play": "Plays", "account_out": "Account offline", "buy": "Please recharge to watch the video"}, "load": {"pulling-text": "Pull down to refresh....", "loosing-text": "Release your hand to refresh...", "loading-text": "Loading..."}, "setBank": {"bank_id": "ID", "bank_name": "Bank name", "user_name": "Depositor name", "bank_account": "Account number"}}