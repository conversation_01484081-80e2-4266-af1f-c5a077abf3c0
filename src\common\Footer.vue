<template>
  <van-tabbar
    v-if="show"
    v-model="active"
    active-color="#ff1a6d"
    :border="true"
    inactive-color="#8f80ad"
  >
  <!-- 首页 -->
    <van-tabbar-item to="/Home">
      <span>{{ $t("tabbar.index") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/index_select.png' : '/img/footer/index.png'"
          :alt="$t('tabbar.index')"
        />
      </template>
    </van-tabbar-item>
    <!-- 视频 -->
    <van-tabbar-item to="/Video">
      <span>{{ $t("tabbar.video") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/video_select.png' : '/img/footer/video.png'"
          :alt="$t('tabbar.video')"
        />
      </template>
    </van-tabbar-item>
    <!-- 中间 -->
    <van-tabbar-item to="/Goods">
      <span>{{ $t("tabbar.goods") }}</span>
      <template #icon="props">
        <div class="wrap">
          <div class="yuepao">
            <img
              src="/img/footer/yuepao.png"
            />
          </div>
        </div>
      </template>
    </van-tabbar-item>
    <!-- 视频 -->
    <van-tabbar-item to="/Lottery">
      <span>{{ $t("tabbar.message") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/dashang_select.png' : '/img/footer/dashang.png'"
          :alt="$t('tabbar.message')"
        />
      </template>
    </van-tabbar-item>
    <!-- 我的 -->
    <van-tabbar-item to="/Mine">
      <span>{{ $t("tabbar.user") }}</span>
      <template #icon="props">
        <img
          :src="props.active ? '/img/footer/user_select.png' : '/img/footer/user.png'"
          :alt="$t('tabbar.user')"
        />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      active: 0
    };
  },
  methods: {},
  watch: {
    $route(to) {
      console.log(to)
      if (to.name == "home") {
        this.active = 0;
        this.show = true;
      } else if (to.name == "video") {
        this.active = 1;
        this.show = true;
      } else if (to.name == "Goods") {
        this.active = 2;
        this.show = true;
      } else if (to.name == "Lottery") {
        this.active = 3;
        this.show = true;
      } else if (to.name == "mine") {
        this.active = 4;
        this.show = true;
      } else {
        this.show = false;
      }
    },
  },
  created() {
    if (this.$route.name == "home") {
      this.active = 0;
      this.show = true;
    } else if (this.$route.name == "video") {
      this.active = 1;
      this.show = true;
    } else if (this.$route.name == "Goods") {
      this.active = 2;
      this.show = true;
    } else if (this.$route.name == "Lottery") {
      this.active = 3;
      this.show = true;
    } else if (this.$route.name == "mine") {
      this.active = 4;
      this.show = true;
    } else {
      this.show = false;
    }
  },
};
</script>

<style lang="less" scoped>
@tabbar-height: 3.75rem;
@tabbar-img: 1.38rem;
.van-tabbar {
  width: 31.25rem;
  height: @tabbar-height;
  background: #fafafa;
  box-shadow: 0 0 0.63rem rgba(0, 0, 0, .1);
  left: calc(50vw - 15.625rem);
}
.van-tabbar-item__icon img {
  height: @tabbar-img;
}
.van-tabbar-item {
  font-size: 0.75rem;
  background: #fafafa;
  z-index: 11;
}

span {
  position: relative;
  z-index: 100;
}

[class*="van-hairline"]::after {
  border: none !important;
}
.wrap {
  width: 1.38rem;
  height: 1.38rem;
}
.yuepao {
  width: 3.66667rem;
  height: 3.66667rem;
  position: relative;
  // left: -1.8rem;
  left: -1.3rem;
  right: 0;
  top: -1.66667rem;
  margin: auto;
  border: .6rem solid #fafafa;
  background: #fafafa;
  border-radius: 50%;
  z-index: 1;
  img {
    width: 2.81rem;
    height: 2.81rem;
  }
}


@media (max-width: 31.25rem) {
  .van-tabbar {
    width: 100%;
    left: calc(50vw - 50%);
  }
}
</style>
