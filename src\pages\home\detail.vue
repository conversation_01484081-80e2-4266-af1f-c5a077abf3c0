<template>
  <div class="main">
    <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#000" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #000;">{{ xuanfeiInfo.xuanfei_name }}</span>
      </template>
      <template #right>
        <van-image @click="toKefu" :src="`/img/home/<USER>" width="24"></van-image>
      </template>
    </van-nav-bar>

    <div id="content">

      <div class="swiper">
        <van-swipe class="my-swipe" :show-indicators="false" @change="onChange" :autoplay="3000">
          <van-swipe-item v-for="(item, key) in xuanfeiList" :key=key>
            <!-- <van-image :src="item" fit="cover"></van-image> -->
            <img :src="item" alt="" style="height: 31.25rem; width: 100%; object-fit: cover;">
          </van-swipe-item>
          <template #indicator>
            <div class="custom-indicator">{{ current + 1 }} / {{ xuanfeiList.length }}</div>
          </template>
        </van-swipe>
        <div class="swiper-fot">
          <van-image src="img/home/<USER>" width="81"></van-image>
          <div class="renzheng">
            <div>{{ $t('xuanfeiDetail.label1') }}</div>
            <div>{{ $t('xuanfeiDetail.label2') }}</div>
          </div>
        </div>
      </div>

      <div class="code">
        <div>{{ xuanfeiInfo.xuanfei_name }}</div>
        <div>{{ xuanfeiInfo.number }}</div>
      </div>

      <div class="content">
        <div class="title">소녀 프로필</div>
        <div class="row">
          {{ `${$t('xuanfeiDetail.label3')}：${xuanfeiInfo.height}cm ${$t('xuanfeiDetail.label4')}：${xuanfeiInfo.bust}` }}
        </div>
        <div class="biaoqian_wrap">
          <div class="biaoqian" v-for="(item, index) in xuanfeiInfo.type" :key="index">{{ item.name }}</div>
        </div>
        <div class="row">
          <div>
            <span>{{ $t('xuanfeiDetail.label5') }}：</span>
            <van-icon name="star" color="#f4b757" size="1.125rem" />
            <van-icon name="star" color="#f4b757" size="1.125rem" />
            <van-icon name="star" color="#f4b757" size="1.125rem" />
            <van-icon name="star" color="#f4b757" size="1.125rem" />
            <van-icon name="star" color="#f4b757" size="1.125rem" />
          </div>
        </div>
        <div class="flex">
          <div style="flex: 1">
            <div class="row">{{ `서비스가능지역：${xuanfeiInfo.diqu}` }}</div>
            <div class="row">{{ `이동가능여부：${xuanfeiInfo.diqu}` }}</div>
          </div>
          <!-- <div style="flex: 1" class="button2" @click="goGame">
            {{ $t('xuanfeiDetail.button') }}
          </div> -->
        </div>
        <div class="title" style="margin-top: 2rem;">{{ $t('xuanfeiDetail.title2') }}</div>

        <van-image v-for="(item, index) in xuanfeiList" :key="index" :src="item" style="margin-top: .3rem;"></van-image>
        <div class="title" style="margin-top: 0.63rem;">{{ $t('xuanfeiDetail.title3') }}</div>

        <div class="video_player">
          <video ref="video" class="video" controls playsinline webkit-playsinline preload="metadata" :autoplay = auto>
            <source :src="xuanfeiInfo.vod" />
          </video>
        </div>

        <div class="title2" style="font-size: 1.03rem; color: #534589;">{{ $t('xuanfeiDetail.title4') }}</div>
        <div class="row2" style="font-size: 0.97rem; color: #534589">{{ $t('xuanfeiDetail.text') }}</div>
      </div>
    </div>

    <div class="kefu">
      <div class="button">{{  $t('xuanfeiDetail.kefu') }}</div>
    </div>

    <van-popup v-model="windowShow" :style="{ width: '90%', height: '88%' }">
      <WindowLottery :detail="gameData" :window="true" />
    </van-popup>
  </div>
</template>


<script>
import "video.js/dist/video-js.css";
import WindowLottery from "../lottery/windowLottery.vue";
export default {
  components: { WindowLottery },
  data() {
    return {
      xuanfeiInfo: {},
      xuanfeiList: [],
      current: 0,
      auto: false,
      gameList: [],
      gameData: {},
      windowShow: false
    };
  },
  mounted() {
    this.getxuanfeidata()
    this.getGameItem()
    const userAgent = window.navigator.userAgent;
    const isMobile = /Mobile/i.test(userAgent);
    const isTablet = /Tablet/i.test(userAgent);
    this.auto = isMobile || isTablet
  },
  methods: {
    back() {
      return window.history.back();
    },
    onChange(index) {
      this.current = index
    },
    getxuanfeidata() {
      let data = JSON.parse(localStorage.getItem('xuanfeiDetail') || '{}')
      this.xuanfeiInfo = data
      this.xuanfeiList = data?.img_url
      console.log(data)
    },
    goGame() {
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        this.windowShow = true
      }
    },
    getGameItem(){
      this.$http({
        method: 'get',
        url: 'lottery_list'
      }).then(res=>{
        let gameData = res.data[0]
        gameData.ico = this.xuanfeiInfo.img_url[0]
        console.log(gameData)
        this.gameData = gameData
      })
    },
    toKefu() {
      window.open(this.$store.getters.getBaseInfo.kefu)
    }
  }
};
</script>

<style lang="less" scoped>
.main {
  padding-bottom: 4.06rem;
}
.swiper {
  position: relative;
  height: 31.25rem;
  overflow: hidden;

  .swiper-fot {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2.66667rem;
    background: rgba(0, 0, 0, .9);
    border-radius: 1.66667rem 1.66667rem 0 0;
    z-index: 9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2% 0 5%;
  }

  .renzheng {
    display: flex;

    div {
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(180deg, #e7caaf, #fff7ed);
      color: #a4826b;
      font-weight: 600;
      margin-left: .2rem;
      padding: .2rem .6rem;
      font-size: .75rem;

      &::before {
        content: '';
        width: 1rem;
        height: 1rem;
        background: url('/public/img/home/<USER>');
        background-size: 100% 100%;
      }

      &:first-child {
        border-radius: 0 0 0 1rem;
      }

      &:last-child {
        border-radius: 0 1rem 0 0;
      }
    }

  }

  .custom-indicator {
    color: #fff;
    text-align: right;
    padding-right: .66667rem;
    top: 26.33333rem;
    position: absolute;
    right: 0;
  }
}

.code {
  display: flex;
  background-image: linear-gradient(-244deg, rgb(184, 203, 184) 0%, rgb(184, 203, 184) 0%, rgb(180, 101, 218) 0%, rgb(207, 108, 201) 33%, rgb(238, 96, 156) 66%, rgb(238, 96, 156) 100%);
  height: 3.25rem;
  align-items: center;
  justify-content: center;
  color: #fff;

  div {
    font-weight: bold;

    &:first-child {
      font-size: 1.13rem;
      margin-right: 0.63rem;
    }

    &:last-child {
      font-size: 1rem;
    }
  }
}

.content {
  background-color: #fff;
  padding: 0.81rem;

  .title {
    font-size: 1.19rem;
    padding-bottom: 1rem;
  }

  .row {
    font-size: 0.83rem;
    color: #333;
    margin: 0.38rem 0;
  }


  .biaoqian_wrap {
    display: flex;
    flex-wrap: wrap;
    margin: 0.3rem 0 0 0;

    .biaoqian {
      background: linear-gradient(30deg, #7d76ef, #d63f8c);
      color: #fff;
      display: inline-block;
      border-radius: .33333rem;
      margin-right: .33333rem;
      font-size: 12PX;
      padding: .33333rem 1rem;
      margin-bottom: .33333rem;
    }
  }

  .video_player {
    .video {
      width: 100%;
    }
  }

  .title2 {
    padding-top: 1rem;
    font-weight: bold;
  }

  .row2 {
    padding: .67rem 0;
  }

  .flex {
    display: flex;
    position: relative;
  }

  .button2 {
    background: linear-gradient(20deg, #e73266, #ee5380);
    color: #fff;
    text-align: center;
    padding: 0.43rem 1rem;
    border-radius: 0.33rem;
    height: 2rem;
    position: absolute;
    right: 0;
    bottom: .67rem;
    font-size: 0.83rem;
  }
}

.kefu {
  position: fixed;
  bottom: 0;
  height: 4.06rem;
  width: 100%;
  background: #fff;
  box-shadow: 0 0 .33333rem rgba(0, 0, 0, .35);
  display: flex;
  align-items: center;
  justify-content: center;
  .button {
    width: 70%;
    height: 2.50rem;
    background: linear-gradient(90deg,#fc78af,#f22678);
    border-radius: 0.19rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: #fff;
    margin: 0;
  }
}
</style>