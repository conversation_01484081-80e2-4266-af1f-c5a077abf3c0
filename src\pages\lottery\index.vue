<template>
  <div class="main">
    <div class="head">{{ $t('lottery.title') }}</div>
    <!-- <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ lottery.name }}</span>
      </template>
    </van-nav-bar> -->

    <div id="content">

      <div class="top">
        <div class="left">
          <van-image src="/img/lottery/gs.png">
            <template slot="loading">
              <van-loading type="spinner" size="20" />
            </template>
          </van-image>
        </div>
        <div class="right">
          <div>
            <div class="title">{{this.lottery.now_expect}} 회차</div>
            <div class="time"><van-count-down :time="time" @finish="check()" /></div>
          </div>
          <div class="bottom">
            <div class="ball">
              <van-image class="res-img" :src="this.shanzi_1">
                <template v-slot:loading>
                  <van-loading type="spinner"/>
                </template>
              </van-image>
              <van-image class="res-img" :src="this.shanzi_2">
                <template v-slot:loading>
                  <van-loading type="spinner"/>
                </template>
              </van-image>
              <van-image class="res-img" :src="this.shanzi_3">
                <template v-slot:loading>
                  <van-loading type="spinner"/>
                </template>
              </van-image>
            </div>
            <div class="sum" @click="active ? active = false : active = true" >
              <span>{{this.sum}}</span>
              <span>{{this.size}}</span>
              <span>{{this.double}}</span>
              <span>
                <van-icon name="arrow-down" :class="{ up: active,down:!active }"/>
              </span>
            </div>
          </div>
        </div>
      </div>


      <div class="card">
        <div class="bar">
          <span>【{{this.lottery.desc}}】</span>
          <span>
            <img src="/img/lottery/xx.png">
            <span class="span-text" @click="$router.push({path:'/GameRecord'});">{{$t("my.task_record")}}</span>
          </span>
        </div>
        <div class="line"></div>
        <div class="block_group">
          <div class="block_wrap" :class="{active:choose[v.type]}" v-for="(v,key) in lottery_peilv_list" :key="key" @click="choosePlay(v.type,v.name);">
            <div class="block">{{v.name}}</div>
          </div>
        </div>
      </div>
    </div>



    <div class="bottom-bar">
      <van-row type="flex" class="bar-row">
        <van-col span="6">
          <van-row @click="shopping ? shopping = false : shopping = true ">
            <van-col span="24">
            <van-icon name="cart-o" class="jixuanico" color="#fff" size="30" />
            </van-col>
            <van-col span="24">
              <span class="text">{{$t("reservation.task_list")}}</span> 
            </van-col>
          </van-row>
        </van-col>
        <van-col span="12">
          <van-row>
            <van-col span="24">
              <span class="text">{{$t("reservation.available_balance")}}</span>
              </van-col>
              <van-col span="24">
                <span class="text_num">{{ this.userInfo.money }}</span>
            </van-col>
            <van-col span="24">
                <span class="text">{{$t("reservation.unit")}}</span>
            </van-col>
          </van-row>
        </van-col>
        <van-col span="6">
          <div class="jie-submit" @click="jiesuan()">{{$t("reservation.submit")}}</div>
        </van-col>
      </van-row>
      <div class="detail" :class="{active:shopping}">
      <div class="item">
        <span class="label">{{$t("reservation.curr_choose")}}：</span>
        <div class="bet-number">{{ this.shopchoose }}</div>
        <van-icon name="arrow-down" color="#fff" :class="{ up: !shopping,down:shopping }" @click="shopping ? shopping = false : shopping = true" />
      </div>
      <div class="link"></div>
    <div class="item">
      <span class="label">{{$t("reservation.per_price")}}:</span>
      <div class="amount-wrapper">
        <van-field class="sds" v-model="money" type="digit" :placeholder="$t('reservation.price_place')" />
        <span class="label">{{$t("reservation.unit")}}</span>
      </div>
    </div>
    <div class="link"></div>
    <div class="item">
      <div class="part" style="margin-right: 1rem;">
        <span>{{$t("reservation.total")}}</span>
        <span class="number">{{this.formData.length}}</span>
        <span>{{$t("reservation.note")}}</span>
      </div>
      <div class="part">
        <span>{{$t("reservation.total")}}</span>
        <span class="number">{{this.formData.length * this.money}}</span>
        <span>{{$t("reservation.unit")}}</span>
      </div>
    </div>
    </div>
    </div>
    <van-popup v-model="active" position="top" :style="{ height: '70%' }" >
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
        <div class="ball-history-list">
          <div class="item">
            <div class="left font-weight">{{$t("reservation.num")}}</div>
              <div class="right font-weight" >{{$t("reservation.win_num")}}</div>
            </div>
            <div class="item" v-for="(v,key) in lottery_list" :key="key">
            <div class="left font-weight">{{v.expect}}</div>
              <div class="right font-weight" >
                <div class="kuaisan-ball left">
                  <van-image class="res-img" :src="'img/lottery/shaizi/' + v.opencode[0] + '.png'">
                    <template v-slot:loading>
                      <van-loading type="spinner"/>
                    </template>
                  </van-image>
                  <van-image class="res-img" :src="'img/lottery/shaizi/' + v.opencode[1] + '.png'">
                    <template v-slot:loading>
                      <van-loading type="spinner"/>
                    </template>
                  </van-image>
                  <van-image class="res-img" :src="'img/lottery/shaizi/' + v.opencode[2] + '.png'">
                    <template v-slot:loading>
                      <van-loading type="spinner"/>
                    </template>
                  </van-image>
                  <span class="res-des middle">{{v.opencode[0] + v.opencode[1] + v.opencode[2]}}</span>
                  <span class="res-des middle">{{(v.opencode[0] + v.opencode[1] + v.opencode[2]) >= 11 && (v.opencode[0] + v.opencode[1] + v.opencode[2]) &lt;= 18 ? $t("my.big") : $t("my.small") }}</span>
                  <span class="res-des middle">{{(v.opencode[0] + v.opencode[1] + v.opencode[2]) % 2 === 0 ?  $t("my.double")  :  $t("my.single") }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-pull-refresh>
    </van-popup>
    <van-popup v-model="jiesuanpage">
      <div class="confirm-order-modal">
        <div class="head van-hairline--bottom">
          <p class="text">{{$t("reservation.task_list")}}</p>
        </div>
        <ui class="list">
            <li v-for="(v,key) in formData" :key="key" class="lise-item van-hairline--bottom">
                <div class="main-model">
                  <p class="bet-name">{{ v.name }}</p>
                  <p class="detail-text">1{{$t("reservation.note")}}X{{ money }}{{$t("reservation.unit")}}={{ money }}{{$t("reservation.unit")}}</p>
                </div>
                <van-icon @click="clearChooes(v.type)" name="close" />
            </li>
        </ui>
        <div class="sub-bar">
          <van-button class="item cancel-btn" type="default" @click="allClear()">{{$t("reservation.clear_order")}}</van-button>
          <van-button class="item sub-btn" type="default" @click="doSub">{{$t("reservation.submit")}}</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
var time;
var count = 0;
export default {
  data() {
    return {
      jiesuanpage:false,
      choose: {
        "bi":false,
        "sm":false,
        "si":false,
        "do":false,
        "3":false,
        "4":false,
        "5":false,
        "6":false,
        "7":false,
        "8":false,
        "9":false,
        "10":false,
        "11":false,
        "12":false,
        "13":false,
        "14":false,
        "15":false,
        "16":false,
        "17":false,
        "18":false,
      },
      playgame:false,
      shopping:false,
      isLoading: false,
      play:{},
      lottery_peilv_list:{},
      lottery_list:{},
      active: false,
      userInfo:{},
      lottery:{},
      shanzi_1:"0",
      shanzi_2:"0",
      shanzi_3:"0",
      sum:0,
      size:"",
      double:"",
      time:0,
      shopchoose:this.$t("reservation.no_choose"),
      gameitem:"",
      formData:[],
      money:"",
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    doSub(){
      if(this.money === "0"){
        this.$toast(this.$t("reservation.money_err"));
        return false;
      }
      if(this.formData.length === 0){
        this.$toast(this.$t("reservation.choose_num"));
        return false;
      }else if(this.money === ""){
        this.$toast(this.$t("reservation.price_place"));
        return false;
      } else {
        if(this.userInfo.money - (this.money * this.formData.length) < 0 ){
          this.$toast(this.$t("reservation.balance_enough"));
          return false;
        }else {
          this.$http({
            method: 'post',
            data:{
               item:this.gameitem,
               money:this.money,
               lid:this.lottery.id,
               mid:this.userInfo.id,
               expect:this.lottery.now_expect
            },
            url: 'game_place_order'
          }).then(res=>{
            if(res.code === 200){
              this.$toast(res.msg);
              this.allClear();
              this.getUserInfo();
            }else if(res.code === 401){
              this.$toast(res.msg);
            }
          })
          return true;
        }
      }
    },
    allClear(){
      for(var i = 0;i<this.formData.length;i++){
          this.choose[this.formData[i]['type']] = false;
      }
      this.formData.length = 0;
      this.money = "";
      this.shopchoose = this.$t("reservation.no_choose");
      this.gameitem ="";
      this.shopping = false;
      this.jiesuanpage = false;
    },
    jiesuan(){
      if(this.formData.length === 0){
        this.$toast(this.$t("reservation.choose_num"));
        return false;
      }else if(this.money === ""){
        this.$toast(this.$t("reservation.price_place"));
        return false;
      }
      else {
        this.jiesuanpage ? this.jiesuanpage = false : this.jiesuanpage = true;
      }

    },
    clearChooes(type){
      for(var i = 0;i<this.formData.length;i++){
        if(type === this.formData[i]['type'] ){
          this.formData.splice(i,1)
          this.choose[type] = false;
        }
      }
      if(this.formData.length >= 1){
        for(var j = 0;j < this.formData.length;j++){
          if(j === 0){
            this.shopchoose = this.formData[j]['name'];
            this.gameitem = this.formData[j]['type'];
          }else {
            this.shopchoose += ","+this.formData[j]['name'];
            this.gameitem   += "," + this.formData[j]['type'];
          }
        }
      }else {
        this.shopchoose = this.$t("reservation.no_choose");
        this.gameitem = "";
        this.shopping = false;
      }
      if(this.formData.length === 0){
        this.jiesuanpage = false;
      }
    },
    choosePlay(type,name){
        if(this.choose[type] === true){
          this.choose[type] = false;
          for(var i = 0;i<this.formData.length;i++){
            if(type === this.formData[i]['type'] ){
                this.formData.splice(i,1)
            }
          }
        }else if(this.choose[type] === false) {
          this.formData.push({'name':name, 'type':type})
          this.choose[type] = true;
        }
        if(this.formData.length === 1){
          this.shopping = true;
        }
        if(this.formData.length >= 1){
          for(var j = 0;j < this.formData.length;j++){
            if(j === 0){
              this.shopchoose = this.formData[j]['name'];
              this.gameitem = this.formData[j]['type'];
            }else {
              this.shopchoose += ","+this.formData[j]['name'];
              this.gameitem += ","+this.formData[j]['type'];
            }
          }
        }else {
          this.shopchoose = this.$t("reservation.no_choose");
          this.gameitem = "";
          this.shopping = false;
        }

    },
    check(){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        time = window.setInterval(() => {
          setTimeout(()=>{
            this.getUserInfo();
            this.getLotteryInfo();
            this.getLotteryList();
            count++;
            if(count > 5){
              clearInterval(time);
              count = 0;
            }
          },0)
        }, 300)
      }
    },
    onRefresh() {
      setTimeout(() => {
        this.$toast(this.$t("reservation.refresh"));
        this.getLotteryList();
        this.isLoading = false;
      }, 200);
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        console.log(res)
        if(res.code === 200){
          this.userInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryPeilv(){
      this.$http({
        method: 'get',
        data:{id: 10},
        url: 'lottery_get_peilv'
      }).then(res=>{
        if(res.code === 200){
          this.lottery_peilv_list = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryList(){
      this.$http({
        method: 'get',
        data:{key: 'game1'},
        url: 'lottery_get_one_list'
      }).then(res=>{
        if(res.code === 200){
          this.lottery_list = res.data;
          this.getLotteryPeilv();
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryInfo(){
      this.$http({
        method: 'get',
        data:{key: 'game1'},
        url: 'lottery_get_info'
      }).then(res=>{
        if(res.code === 200){
          if(parseFloat(this.userInfo.money) < parseFloat(res.data.condition)){
            this.$toast(this.$t("reservation.contact_admin"));
            this.$router.push({path:'/Home'})
            return false;
          }
          this.lottery = res.data;
          this.time = res.data.second * 1000;

          if(this.time/1000 === 59){
            this.$toast(this.$t("reservation.prize_succ")+this.lottery.now_expect);
          }
          this.shanzi_1 = "img/lottery/shaizi/" + res.data?.opencode?.[0] + ".png";
          this.shanzi_2 = "img/lottery/shaizi/" + res.data?.opencode?.[1] + ".png";
          this.shanzi_3 = "img/lottery/shaizi/" + res.data?.opencode?.[2] + ".png";
          this.sum = res.data.opencode?.[0] + res.data.opencode?.[1] + res.data.opencode?.[2];
          if(this.sum >= 11 && this.sum <=18){
            this.size = this.$t("my.big");
          }else if(this.sum >= 3 && this.sum <= 10){
            this.size = this.$t("my.small");
          }
          if(this.sum % 2 === 0){
            this.double = this.$t("my.double");
          }else {
            this.double = this.$t("my.single");
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })

    }
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
      this.getLotteryInfo();
      this.getLotteryList();

    }
  },
  destroyed() {
    clearInterval(time);
  }
};
</script>

<style lang='less' scoped>
.main{
  background: none;
  overflow: auto;
  height: 100%;
  position: relative;
  height: calc(var(--vh) * 100 - 3.75rem);
  background: url('/public/img/bg.png') repeat top / 100% auto;
  .head {
    display: flex;
    justify-content: center;
    height: 2.88rem;
    font-weight: bold;
    align-items: center;
  }
}

#content{
  padding-top: 0;
  padding-bottom: 4.6875rem;
}
/deep/ .nav-bar {
  background: linear-gradient(90deg, #b2acbb00, #a1b1e600) !important;
}

@keyframes rotate {
  100% {
    transform: rotate(1turn)
  }
}

.top{
  display: flex;
  background: linear-gradient(264deg, #7C59A5 0%, #EE7DD4 100%);
  border-radius: 1.13rem;
  margin: 1.8rem 2.5%;
  padding: 2.13rem 3%;
  .left{
    width: 35%;
    /deep/ .van-image__img{
      min-width: 7.38rem;
      min-height: 7.38rem;
    }
  }
  .right{
    width: 65%;
    padding-left: 5%;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    .title{
      font-size: 1.2rem;
      font-weight: bold;
      color: #fff;
      margin-bottom: .5rem;
    }
    /deep/ .van-count-down {
      color: #fff;
      font-size: 1.1rem;
    }
    .bottom{
      display: flex;
      color: #fff;
      justify-content: space-between;
      .ball{
        display: flex;
        .res-img{
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.63rem;
        }
      }
      .sum{
        display: flex;
        align-items: center;
        cursor: pointer;
        span {
          margin-left: 0.63rem;
          line-height: 1rem;
        }
      }
    }
  }
}

.card{
  margin: 0 auto;
  width: 90%;
  position: relative;
  flex: 1;
  overflow: hidden;
  .bar{
    display: flex;
    justify-content: space-between;
    color: #ff1a6d;
    height: 3.63rem;
    align-items: center;
    img{
      width: 1rem;
      height: 1.25rem;
      display: block;
      margin-right: 0.51rem;
    }
    span{
      display: flex;
    }
  }
  .line{
    width: 100%;
    height: 0.06rem;
    background: linear-gradient(90deg, rgba(80, 59, 143, 0.05), rgba(80, 59, 143, 0.61), rgba(80, 59, 143, 0.05));
  }

  .block_group{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 1.25rem 0;
    .block_wrap{
      width: 50%;
    }
    .block{
      width: 7.50rem;
      height: 7.50rem;
      text-align: center;
      line-height: 7.5rem;
      margin: 0 auto 1.75rem;
      border-radius: 0.38rem;
      border: 0.13rem solid #a06098;
      font-size: 1.63rem;
      font-weight: bold;
      color: #a06098;
      background: #fff;
    }
    .active{
      .block{
        background: #a06098;
        color: #fff;
      }
    }
  }
}


.bottom-bar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 4.6875rem;
  background: #39043F;
  opacity: .9;
  z-index: 2;
  color: #fff;
  .bar-row {
    height: 4.6875rem;
    text-align: center;
    align-items: center; /* 垂直居中 */
    .text_num {
      color: #F33DDB;
      margin: .2rem 0;
      display: inline-block;
    }
    .jie-submit {
      color: #fff;
      font-size: .9375rem;
      height: 2.18rem;
      line-height: 2.18rem;
      background: #F33DDB;
      border-radius: 0.22rem;
      width: 4.75rem;
    }
  }
  .detail {
    background: #39043F;
    display: none;
    &.active{
      transform: translateY(-155%);
      display: block;
    }
    .item {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 3%;
      padding-right: 3%;
      padding-top: .625rem;
      padding-bottom: .625rem;
      .label {
        font-size: .9375rem;
        line-height: .9375rem;
        color: #fff;
      }
      .bet-number {
        flex: 1;
        margin: 0 .5rem;
        overflow: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        color: #ccc;
        font-size: .9375rem;
        font-weight: 500;
        height: 1.25rem;
        line-height: 1.25rem;
      }
      .up {
        transform: rotate(180deg);
        transition: all .5s;
      }
      .down {
        transition: all .5s;
      }
      .amount-wrapper {
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
        .sds {
          background-color: inherit !important;
          border-bottom: 0 !important;
          font-size: .9375rem;
          line-height: 1.5625rem;
          /deep/ .van-field__control {
              color: #F33DDB;
          }
        }
      }
    }
    .link {
      width: 100%;
      height: .03125rem;
      background: #fff;
      opacity: .1;
    }
  }
}
.ball-history-list{
  position: relative;
  flex: 1;
  overflow: hidden;
  background: #fff;
  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: .3125rem 0;
    padding-left: 3%;
    padding-right: 3%;
    .left {
      width: 40%;
      font-size: .9375rem;
      text-align: center;
      font-weight: 500;
      color: #000;
    }
    .right {
      flex: 1;
      display: flex;
      font-size: .9375rem;
      justify-content: center;
      overflow: hidden;
      color: #000;
      .kuaisan-ball {
        margin-left: .625rem;
        flex: 1;
        display: flex;
        align-items: center;
         .res-img {
          width: 1.5625rem;
          height: 1.5625rem;
          margin-right: .625rem;
        }
        .res-des {
          font-weight: 700;
          text-align: center;
          color: #000;
          &.middle {
            width: 15%;
            font-size: 1.09375rem;
          }
        }
      }
      .left {
        width: 40%;
        font-size: .9375rem;
        text-align: center;
        font-weight: 500;
        color: #000;
      }
    }

    .font-weight {
      font-weight: 700 !important;
    }
  }
}

/deep/ .van-popup{
  background: none;
}

.confirm-order-modal {
  position: unset;
  display: flex;
  flex-direction: column;
  margin: auto;
  padding: 0 2.667% 4%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 23.25rem;
  height: 26rem;
  max-height: 50%;
  z-index: 10;
  background-color: #fff;
  border-radius: 0.75rem;
  .head {
    position: relative;
    height: 2.81rem;
    .text {
      line-height: 2.81rem;
      text-align: center;
      font-size: 1.25rem;
      color: #7e5678;
      margin: 0;
    }
  }
  .list {
    flex: 1;
    padding: 0 0.38rem;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    .lise-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0.38rem 0;
      color: #a06098;
      .main-model {
        flex: 1;
        overflow: hidden;
        .bet-name {
          color: #a06098;
          font-size: 1.25rem;
          font-weight: 500;
          line-height: 0;
          word-wrap: break-word;
          word-break: break-all;
        }
        .detail-text {
          line-height: 0;
          font-size: 0.88rem;
          color: #979799;
        }
      }
    }
  }
  .sub-bar {
    display: flex;
    align-items: center;
    margin-top: 1rem;
    justify-content: space-around;
    .item {
      min-width: 40%;
      height: 2.5rem;
      text-align: center;
      box-sizing: border-box;
      border-radius: 3.13rem;
      font-size: 1.09375rem;
      font-weight: 500;
      &.cancel-btn {
        border: .03rem solid #979799;
        color: #979799;
        background-color: #fff;
      }
      &.sub-btn {
        background: linear-gradient(270deg, #e6c3a1, #7e5678);
        color: #fff;
        border: 0;
      }
    }
  }
}

.rectangle.active .wrapper {
  background-color: #a06098 !important;
}
</style>
