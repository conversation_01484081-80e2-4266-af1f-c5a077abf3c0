<template>
  <div class="container">
    <van-nav-bar :title="$t('setting.fill_bank')">
      <template #left>
        <van-icon name="arrow-left" color="#412188" size="20"  @click="back()"></van-icon>
      </template>
    </van-nav-bar>
    
    <div class="card">
      <div class="row">
        <div class="title">{{ $t('setting.band_name') }}</div>
        <div class="input_wrap">
          <van-field v-model="bank" readonly :placeholder="$t('setting.band_name')" @click="showSelectBanks()"/>
        </div>
      </div>
      <div class="row">
        <div class="title">{{ $t('setting.band_account') }}</div>
        <div class="input_wrap">
          <van-field v-model="bankid" type="digit" :placeholder="$t('setting.band_account')" />
        </div>
      </div>
      <div class="row">
        <div class="title">{{ $t('setting.username') }}</div>
        <div class="input_wrap">
          <van-field v-model="name" :placeholder="$t('setting.username')" />
        </div>
      </div>
      <div class="row">
        <div class="title">{{ $t('setting.password') }}</div>
        <div class="input_wrap">
          <van-field v-model="paypassword" type="password" :placeholder="$t('setting.password')" />
        </div>
      </div>
      <div class="row">
        <div class="title">{{ $t('setting.password2') }}</div>
        <div class="input_wrap">
          <van-field v-model="paypassword2" type="password" :placeholder="$t('setting.password2')" />
        </div>
      </div>
    </div>
    <div class="btn" @click="bindCard()">{{$t("setting.save_password")}}</div>
    <van-popup v-model="showBank" round position="bottom" :style="{ height: '35%' }" >
      <van-picker
          show-toolbar
          :columns="banks"
          @confirm="onConfirm"
          @cancel="onCancel"
          confirm-button-text="확정"
          cancel-button-text="취소"
      />
    </van-popup>
  </div>
</template>

<script>

export default {
  data() {
    return {
      banks: [],
      showBank:false,
      userInfo:{},
      bankid:"",
      bank: "",
      name:"",
      paypassword: "",
      paypassword2: "",
      mobile:"",
      bank_code:""
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    async bindCard(){
      if(this.bank === "" || this.bank === null || this.bank === undefined){
        this.$toast.fail(this.$t("setting.band_name_tip"));
        return false;
      }

      if(this.bankid === ""){
        this.$toast(this.$t("setting.band_account_tip"));
        return true;
      }

      if(this.name===""){
        this.$toast(this.$t("setting.band_name_tip"));
        return false;
      }
      
      if(this.paypassword===""){
        this.$toast(this.$t("setting.password_tip"));
        return false;
      }

      if(this.paypassword2===""){
        this.$toast(this.$t("setting.password_tip3"));
        return false;
      }
      
      if(this.paypassword.length !== 4){
        this.$toast(this.$t("setting.password_tip2"));
        return false;
      }

      if(this.paypassword !== this.paypassword2 ){
        this.$toast(this.$t("setting.password_tip4"));
        return false;
      }
      let setname, setbank, setpwd
      
      setname = await this.$http({method: 'post',data:{name:this.name},url: 'user_set_name'}).then(res=>{
        if(res.code !== 200){
          this.$toast(res.msg);
        }
        return res.code === 200
      })
      if(setname) {
        setpwd = await this.$http({method: 'post',data:{paypassword:this.paypassword},url: 'user_set_paypw'}).then(res=>{
          if(res.code !== 200){
            this.$toast(res.msg);
          }
          return res.code === 200
        })
      }
      
      if(setpwd) {
        setbank = await this.$http({method: 'post',data:{bankid:this.bankid, bank: this.bank},url: 'user_set_bank'}).then(res=>{
          this.$toast(res.msg);
          if(res.code === 200){
            setTimeout(() => {
              this.back()
            }, 2000);
          }
        })
      }
      
    },
    showSelectBanks(){
      this.showBank = true;
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          console.log(res.data)
          this.userInfo = res.data;
          this.name = res.data.name;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getBankList(){
      this.$http({
        method: 'get',
        url: 'sys_get_banks'
      }).then(res=>{
        console.log(res)
        if(res.code === 200){
          this.banks = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    onConfirm(value) {
      this.bank = value.text
      this.bank_code = value.value
      this.showBank = false;
    },
    onCancel() {
      this.showBank = false;
    },
    getUserBankInfo(){
      this.$http({
        method: 'get',
        url: 'user_get_bank'
      }).then(res=>{
        if(res.code === 200){
          if(res.data.is_bank){
            this.is_bind = true;
          }else {
            this.is_bind = false;
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    }
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
      this.getBankList();
      this.getUserBankInfo();
    }
  }
};
</script>

<style lang='less' scoped>
@import "../../assets/css/base.css";

.container{
  background: linear-gradient(180deg, #fff, #fefae9);
}

.van-nav-bar {
  background: none;
  height: 2.88rem;
  position: relative !important;

  /deep/ .van-nav-bar__title{
    color: #412188;
    font-size: 1rem;
  }
  /deep/ .van-nav-bar__left{
    padding: 0 1rem;
  }
}


.van-cell {
  font-size: 2.00rem;
  line-height: 5.00rem;
}
/deep/ .van-picker__toolbar {
  height: 3.13rem;
}
/deep/ .van-picker__cancel, /deep/ .van-picker__confirm {
  padding: 0 1.25rem;
  font-size: 1.25rem;
}
/deep/ .van-picker-column {
  font-size: 1.50rem;
}


.card{
  padding: 1.00rem 1.25rem;
  .title{
    color: #49328b;
    font-size: 0.88rem;
  }
  .input_wrap{
    background-color: inherit !important;
    background: #321b51;
    border-radius: 6.25rem;
    border: 0.13rem solid #ccc4cf;
    height: 3.00rem;
    margin-top: 4.5%;
    margin-bottom: 4.5%;
    color: #323233;
    display: flex;
    align-items: center;
  }
  
  .van-field{
    background-color: transparent;
    padding: 0 1.25rem;

    color: #fff;
    &::-webkit-input-placeholder {
      color: #323233;
    }
    font-size: 0.88rem;
    display: flex;
    align-items: center;
    border-bottom: 0 !important;
    border: none;
    height: 1.50rem;
    /deep/ .van-field__control{
      color: #323233;
      background-color: transparent;
    }
    /deep/ .van-field__value{
      width: 100%;
      height: 100%;
      line-height: 2rem;
    }


    
    /deep/ .van-field__body{
      height: 1.50rem;
    }
    
  }
}
.btn{
  display: inline-block;
  width: 90%;
  margin: 0 auto 2rem;
  background: linear-gradient(20deg, #9d50cf, #ee5380);
  color: #fff;
  padding: .86667rem 0;
  border-radius: 3rem;
  font-size: .93333rem;
  cursor: pointer;
  text-align: center;
}
</style>
