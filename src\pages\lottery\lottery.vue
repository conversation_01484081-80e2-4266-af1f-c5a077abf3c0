<template>
  <div class="main">
    <div class="head">
      <div class="title">{{ $t('lottery.title') }}</div>
      <div class="card">
        <div class="row">
          <div class="expand">{{ lottery.next_expect }}{{  $t('lottery.label1') }}</div>
          <div class="time">
            <span>{{ $t('lottery.label2') }}</span>
            <van-count-down :time="time" @finish="check()" />
          </div>
        </div>
        <div class="top">
          <div class="expand">{{ lottery.now_expect }}{{  $t('lottery.label1') }}</div>
          <div class="over">
            <span>{{over1}}</span>
            <span>{{over2}}</span>
          </div>
          <van-icon name="arrow" :class="{ rotate: showHistory }" color="#fff" @click="showHistory = !showHistory" />
        </div>
      </div>
    </div>
    <div class="content">
      <div class="blockList">
        <div class="block" v-for="item in lottery_peilv_list" :class="{ active: lotteryChooseList.findIndex(s => s.id === item.id) >= 0 }" :key="item.id" @click="choosePlay(item)">{{ item.name }}</div>
      </div>
    </div>

    <div class="historyList animate__animated animate__fadeInDown" v-if="showHistory">
      <div class="row top">
        <div class="left">{{  $t('lottery.label8') }}</div>
        <div class="right">{{  $t('lottery.label9') }}</div>
      </div>
      <div class="content">
        <div class="row" v-for="item in lotteryList" :key="item.id">
          <div class="left">{{item.expect}}</div>
          <div class="right">
            <span>{{item.over1}}</span>
            <span>{{item.over2}}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="bottom" :class="{ show: lotteryChooseList.length > 0 }">
      <div class="cart">
        <van-badge :content="lotteryChooseList.length">
          <div class="wrap">
            <img src="/img/lottery/cart.png" alt="">
          </div>
        </van-badge>
      </div>
      <div class="left">
        <div>
          <!-- <span style="margin-right: 0.31rem">{{ $t('lottery.label3') }}</span> -->
          <span class="value" v-for="item in lotteryChooseList" :key="item.id">
            <span>{{ item.name }}</span>
            <span v-if="lotteryChooseList.length > 1 && item.id !== lotteryChooseList[lotteryChooseList.length - 1].id">,</span>
          </span>
        </div>
        <div>{{  $t('lottery.label4') }}</div>
      </div>
      <div class="center">
        <input type="number" v-model="money">
      </div>
      <div class="right">
        <div>
          <span>{{  $t('lottery.label5') }}</span>
          <span class="value">{{ lotteryChooseList.length }}</span>
          <span>{{  $t('lottery.label6') }}</span>
        </div>
        <div>
          <span>{{  $t('lottery.label5') }}</span>
          <span class="value">{{ lotteryChooseList.length * money }}</span>
          <span>{{  $t('lottery.label4') }}</span>
        </div>
      </div>
      <div class="button" @click="openWindow">{{$t("lottery.label7")}}</div>
    </div>
    
    <van-popup v-model="renwudan">
      <div class="window">
        <div class="head">
          <div class="title">
            <span>{{$t("lottery.window.title")}}</span>
            <span class="expand">{{ lottery.now_expect }}</span>
          </div>
          <van-icon class="close" size="24" name="cross" color="#fff" @click="renwudan = false" />
        </div>
        <div class="content">
          <div class="row" v-for="item in lotteryChooseList" :key="item.id">
            <div class="label">{{ item.name }}X{{ money }}</div>
            <van-icon class="close" size="24" name="minus" color="#fff" @click="removeChoose(item)" />
          </div>
        </div>
        <div class="buttonGroup">
          <div class="cancel button" @click="clearChoose">{{  $t('lottery.window.cancel') }}</div>
          <div class="confirm button" @click="confirm">{{  $t('lottery.window.confirm') }}</div>
        </div>
      </div>
    </van-popup>

    
  </div>
</template>
<script>
let interval = null;
let count = 0;

import { waitWithLoading } from "@/common/function";
import { Toast } from 'vant';
export default {
  data() {
    return {
      userInfo: {},
      lottery: {},
      lotteryList: [],
      lottery_peilv_list: [],
      lotteryChooseList: [],
      over1: '',
      over2: '',
      time: 0,
      money: 1,
      showHistory: false,
      renwudan: false,
    };
  },
  computed: {
    gameitem() {
      return this.lotteryChooseList.map(item => item.type).join(',')
    }
  },
  methods: {
    // 获取用户信息
    getUserInfo(){
      return this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          this.userInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryInfo(){
      return this.$http({
        method: 'get',
        data:{key: 'game1'},
        url: 'lottery_get_info'
      }).then(res=>{
        if(res.code === 200){
          this.lottery = res.data;
          this.time = res.data.second * 1000;
          if(this.time/1000 === 59){
            this.$toast(this.$t("reservation.prize_succ")+this.lottery.now_expect);
          }
          const sum = res.data.opencode?.[0] + res.data.opencode?.[1] + res.data.opencode?.[2];
          this.over1 = sum > 9 ? this.$t("lottery.da") : this.$t("lottery.xiao");
          this.over2 = sum % 2 > 0 ? this.$t("lottery.dan") : this.$t("lottery.shuang");
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    
    getLotteryList(){
      return this.$http({
        method: 'get',
        data:{key: 'game1'},
        url: 'lottery_get_one_list'
      }).then(res=>{
        if(res.code === 200){
          this.lotteryList = res.data.map(item => {
            const sum = item.opencode?.[0] + item.opencode?.[1] + item.opencode?.[2];
            item.over1 = sum > 9 ? this.$t("lottery.da") : this.$t("lottery.xiao");
            item.over2 = sum % 2 > 0 ? this.$t("lottery.dan") : this.$t("lottery.shuang");
            return item;
          });
          this.getLotteryPeilv();
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryPeilv(){
      this.$http({
        method: 'get',
        data:{id: 10},
        url: 'lottery_get_peilv'
      }).then(res=>{
        if(res.code === 200){
          this.lottery_peilv_list = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    choosePlay(item){
      if(this.lotteryChooseList.findIndex(s => s.id === item.id) >= 0) {
        this.lotteryChooseList = this.lotteryChooseList.filter(s => s.id !== item.id);
        return;
      }
      this.lotteryChooseList.push(item);
    },
    openWindow(){
      if( !this.money){
        return false;
      }
      this.renwudan = true;
    },
    removeChoose(item){
      this.lotteryChooseList = this.lotteryChooseList.filter(s => s.id !== item.id);
      if(this.lotteryChooseList.length === 0) {
        this.renwudan = false;
      }
    },
    clearChoose() {
      this.lotteryChooseList = [];
      this.renwudan = false;
    },
    confirm() {
      Toast.loading({forbidClick: true})
      this.$http({
        method: 'post',
        data:{
          item: this.gameitem,
          money: this.money,
          lid: this.lottery.id,
          mid: this.userInfo.id,
          expect: this.lottery.now_expect
        },
        url: 'game_place_order'
      }).then(res=>{
        if(res.code === 200){
          this.$toast(res.msg);
          this.clearChoose();
          this.getUserInfo();
        }else if(res.code === 401){
          this.$toast(res.msg);
        }
      })
    },
    
    check(){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        interval = window.setInterval(() => {
          setTimeout(()=>{
            waitWithLoading([
              () => this.getUserInfo(),
              () => this.getLotteryInfo(),
              () => this.getLotteryList()
            ]);
            count++;
            if(count > 5){
              clearInterval(interval);
              count = 0;
            }
          },0)
        }, 300)
      }
    },
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      waitWithLoading([
        () => this.getUserInfo(),
        () => this.getLotteryInfo(),
        () => this.getLotteryList()
      ]);
    }
  },
  destroyed() {
    clearInterval(interval);
  }
};
</script>

<style lang='less' scoped>
.main{
  height: 100%;
  position: relative;
  height: calc(var(--vh) * 100 - 3.75rem);
  overflow: hidden;
  background: url('/public/img/bg.png') repeat top / 100% auto;
  .head {
    background: url(/public/img/lottery/header.png);
    background-size: 100% 100%;
    font-weight: bold;
    color: #fff;
    height: 11.56rem;
    position: relative;
    z-index: 100;
    .title {
      height: 3.00rem;
      display: flex;
      font-size: 1rem;
      align-items: center;
      justify-content: center;
    }
    .card {
      padding: 0.38rem 0.75rem 0.75rem;
      .row {
        display: flex;
        justify-content: space-between;
        font-weight: normal;
        height: 2.06rem;
        padding-bottom: 0.50rem;
        .time {
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }
        .van-count-down{
          color: #20d917;
          font-size: 1.13rem;
          font-weight: 500;
        }
      }
      .top {
        height: 5.44rem;
        background: url(/public/img/lottery/top_bg.png);
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.56rem 0.75rem;
        .expand {
          font-size: 1.13rem;
          font-weight: 500;
          height: 3.00rem;
          border-right: 0.06rem solid #fff;
          padding-right: 1.13rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .over {
          display: flex;
          gap: 0.63rem;
          span {
            background: linear-gradient(180deg,#fc78af,#f22678);
            border-radius: 50%;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 2.5rem;
            width: 2.5rem;
            font-size: 0.88rem;
          }
        }
        .van-icon {
          padding: 1rem;
          transform: rotate(90deg);
          transition: all .3s;
        }
        .rotate {
          transform: rotate(270deg);
        }
      }
    }
  }
  
  .content {
    .blockList {
      padding: 1.38rem 1.38rem 3.75rem;
      display: flex;
      flex-wrap: wrap;
      gap: 1.25rem;
    }
    .block{
      width: calc((100% - 1.25rem) / 2);
      height: 7.50rem;
      line-height: 7.50rem;
      text-align: center;
      font-size: 1.13rem;
      font-weight: 500;
      background: #fff;
      box-shadow: 0 0 .33333rem rgba(0,0,0,.1);
      color: #000;
      border-radius: 0.38rem;
      font-weight: bold;
      border: .33333rem solid #fff;
    }
    .active {
      border: .33333rem solid #f8596f;
      position: relative;
      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        background: url('/public/img/lottery/check.png') no-repeat 101% 100%/1.33333rem;
      }
    }
  }

  .historyList {
    height: calc(var(--vh) * 100 - 10.56rem - 4.5rem);
    position: absolute;
    top: 11.56rem;
    width: 100%;
    background: rgba(0,0,0,.2);
    z-index: 50;
    .content {
      height: calc(var(--vh) * 100 - 10.56rem - 4.5rem - 3.75rem - 4.5rem);
      overflow: auto;
    }
    .top {
      background: #f3f3f3 !important;
    }
    .row {
      height: 3.38rem;
      display: flex;
      background: #f3f3f3;
      &:nth-of-type(2n -1) {
        background: #fff;
      }
      .left {
        width: 40%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 0.06rem solid #ebe8e8;
      }
      .right {
        width: 60%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.63rem;
        span {
          background: linear-gradient(180deg,#fc78af,#f22678);
          border-radius: 50%;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 2.5rem;
          width: 2.5rem;
          font-size: 0.88rem;
        }
      }
    }
  }

  .bottom {
    position: absolute;
    bottom: -4.5rem;
    width: 100%;
    opacity: 0;
    height: 4.13rem;
    background: #3a3635;
    border-radius: 3rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .cart {
      width: 4.13rem;
      height: 4.13rem;
      background: #312e2d;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      img {
        width: 1.88rem;
        height: 1.88rem;
      }
    }
    .left {
      height: 2.75rem;
      color: #fff;
      font-size: 0.8rem;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin: 0 0.3rem;
      .value {
        color: #ed37ae;
      }
    }
    .center {
      input {
        width: 4.06rem;
        text-align: center;
        color: #ed37ae;
        font-size: .8rem;
        height: 1.88rem;
        line-height: 1.88rem;
      }
    }
    .right {
      height: 2.75rem;
      color: #fff;
      font-size: 0.8rem;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin: 0 0.3rem;
      .value {
        color: #ed37ae;
        margin: 0 0.13rem;
      }
    }
    .button {
      width: 4.13rem;
      height: 4.13rem;
      background: #f6287f;
      border-radius:  0 50%  50% 0;
      margin: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
    }
  }
  .show {
    bottom: 0;
    opacity: 1;
    transition: all .3s ease;
  }
  ::v-deep {
    .van-badge {
      width: 1.75rem;
      height: 1.75rem;
      line-height: 1.75rem;
      text-align: center;
      border: 0;
      font-size: 0.88rem;
      top: -1.1rem;
      right: -.2rem;
    }
  }
}

::v-deep .van-popup{
  background: transparent;
}
.window {
  width: 80vw;
  border-radius: 0.31rem;
  overflow: hidden;
  background: transparent;
  .head {
    background: linear-gradient(133deg,#e64588,#f22678);
    height: 2.81rem;
    position: relative;
    .title {
      height: 2.81rem;
    }
    .close {
      position: absolute;
      width: 1.50rem;
      height: 1.50rem;
      right: 1.06rem;
      top: 0.56rem;
    }
  }
  .content {
    padding: 0.75rem 0;
    background: #fff;
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 1.06rem;
      border-bottom: 0.06rem solid #f7f8f9;
    }
    .close {
      background: linear-gradient(133deg,#e64588,#f22678);
      border-radius: 0.31rem;
    }
  }
  .buttonGroup {
    display: flex;
    background: #fff;
    width: 100%;
    justify-content: space-between;
    padding: 3.25rem 1.06rem 1.06rem;
    .button {
      width: 7.31rem;
      height: 2.63rem;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 3rem;
    }
    .cancel {
      background: #fff;
      color: #979799;
      border: 0.06rem solid #979799;
    }
    .confirm {
      background: linear-gradient(90deg,#fa6ba6,#f32879);
      color: #fff;
    }
  }
}

</style>
