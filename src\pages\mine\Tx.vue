<template>
  <div class="container">
    <div class="header" id="content">
      <van-nav-bar :title="$t('recharge.title')">
        <template #left>
          <van-icon name="arrow-left" color="#fff" size="20"  @click="back()"></van-icon>
        </template>
      </van-nav-bar>
      <div class="info">
        <div class="avatar">
          <van-image round class="user_img" :src="userInfo.header_img">
            <template v-slot:error>
              <van-image src="img/nan/11.png"></van-image>
            </template>
          </van-image>
        </div>
        <div class="div">
          <div class="label">{{ userInfo.username }}</div>
          <div class="label">{{ $t('recharge.label') }}：{{ changeMoney(userInfo.money) }}{{ $t('recharge.currency') }}</div>
        </div>
      </div>
    </div>

    <div class="content">
      <div class="card">
        <img src="img/mine/kefu_avatar.png" >
        <div class="label">{{ $t('recharge.info') }}</div>
      </div>
      <div class="button" @click="toKefu">{{ $t('recharge.button') }}</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {},
      recharge_balance: null
    };
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
    }
    
  },
  methods: {
    back(){
      return window.history.back();
    },
    goRecord() {
      this.$router.push("ChongRecord");
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
            this.userInfo = res.data;
            if(this.userInfo.status !== 1){
              this.$toast(this.$t("video.account_out"));
              localStorage.clear()
              this.$router.push({path:'/Login'})
            }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    submit() {
      if(!this.recharge_balance) {
        return
      }
      this.$http({
        url: "userRecharge",
        method: "post",
        data: { 
          amount: this.recharge_balance,
        },
      }).then((res) => {
        if (res.code === 200) {
          this.$toast.success(res.msg);
          // localStorage.setItem("token", res.data.id);
        } else {
          this.$toast(res.msg);
        }
      });
    },
    toKefu() {
      window.open(this.$store.getters.getBaseInfo.kefu)
    }
  }
};
</script>

<style lang='less' scoped>
.container{
  background: url("/public/img/user/withdrawBg.png") no-repeat top / 100%, #fff;
}

.van-nav-bar {
  background: none;
  height: 2.88rem;

  /deep/ .van-nav-bar__title{
    color: #fff;
    font-size: 1rem;
  }
  /deep/ .van-nav-bar__left{
    padding: 0 1rem;
  }
  .record{
    font-size: 0.88rem;
    color: #fff;
  }
}

.header{
  height: 10.00rem;
  background: linear-gradient(41deg, #7d76ef, #e3549d 90%);
  .info{
    height: 4.00rem;
    margin-top: 1.50rem;
    display: flex;
    padding: 0 1.38rem;
    .avatar{
      border: 0.25rem solid #fff;
      background: #f7f8fa;
      border-radius: 6.25rem;
      height: 3.75rem;
      width: 3.75rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 1.13rem;
      .van-image {
        width: 3.25rem;
        height: 3.25rem;
      }
    }
    .label{
      font-size: 1rem;
      color: #fff;
      line-height: 2.00rem;
      height: 2.00rem;
    }
  }
}

.content{
  height: calc(var(--vh) * 100  - 10.00rem);
  background: #fff;
  .card{
    height: 8.13rem;
    padding: 1.50rem;
    margin: 3.75rem 0.81rem 0;
    background: #f2f2f5;
    border-radius: 4.25rem;
    display: flex;
    img {
      width: 3.38rem;
      height: 5.00rem;
    }
    .label {
      margin-left: 0.75rem;
      font-size: 0.94rem;
      color: #390f86;
    }
  }
  .button {
    width: 16.13rem;
    height: 2.75rem;
    background: linear-gradient(30deg,#9b54ca,#e6557f);
    margin: 3.75rem auto 0;
    color: #fff;
    border-radius: 1.88rem;
    line-height: 2.75rem;
    text-align: center;
  }
}

</style>
