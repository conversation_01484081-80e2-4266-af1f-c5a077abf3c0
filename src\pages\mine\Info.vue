<template>
  <div class="container">
    <van-nav-bar :title="$t('userInfo.title')">
      <template #left>
        <van-icon name="arrow-left" color="#412188" size="20"  @click="back()"></van-icon>
      </template>
    </van-nav-bar>

    <div class="row">
      <div class="title">{{ $t("userInfo.bank_id") }}</div>
      <div class="value">{{ bankInfo.bankid || '-' }}</div>
    </div>
    <div class="row">
      <div class="title">{{ $t("userInfo.user_name") }}</div>
      <div class="value">{{ bankInfo.username || '-' }}</div>
    </div>
    <div class="row">
      <div class="title">{{ $t("userInfo.bank_name") }}</div>
      <div class="value">{{ bankInfo.bankinfo || '-' }}</div>
    </div>
    <div class="row">
      <div class="title">{{ $t("userInfo.bank_account") }}</div>
      <div class="value">{{ bankInfo.bank_code || '-' }}</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      is_bind:false,
      bankInfo:{},
      userInfo:{}
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    getUserBankInfo(){
      this.$http({
        method: 'get',
        url: 'user_get_bank'
      }).then(res=>{
        console.log('bankInfo' , res)
        if(res.code === 200){
          this.bankInfo = res.data.info;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        console.log('userInfo' , res)
        if(res.code === 200){
          this.userInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
      this.getUserBankInfo();
    }
  }
};
</script>

<style lang='less' scoped>
.container{
  background: linear-gradient(180deg, #fff, #fefae9);
}

.van-nav-bar {
  background: none;
  height: 2.88rem;
  position: relative !important;

  /deep/ .van-nav-bar__title{
    color: #412188;
    font-size: 1rem;
  }
  /deep/ .van-nav-bar__left{
    padding: 0 1rem;
  }
}

.row{
  display: flex;
  padding: 1.25rem;
  justify-content: space-between;
  height: 1.50rem;
  box-sizing: content-box;
  position: relative;
  &::before{
    position: absolute;
    content: "";
    border: none;
    width: 90%;
    height: 1px;
    background: linear-gradient(90deg, rgba(80, 59, 143, .15), rgba(80, 59, 143, .61), rgba(80, 59, 143, .15));
    bottom: 0;
  }
  .title{
    color: #49328b;
    font-size: 0.88rem;
  }
  .value{
    color: #4141d8;
    font-size: 0.88rem;
  }
}



</style>
