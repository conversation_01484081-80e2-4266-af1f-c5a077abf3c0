<template>
  <div class="bg-container">
    <div class="login">
      <van-nav-bar>
        <template #left>
          <van-icon name="arrow-left" color="#fff" @click="back()"/>
        </template>
      </van-nav-bar>
      <div class="logo">
        <img src="/img/login/logo.png" alt="">
      </div>
      <div class="wrap">
        <div class="input_wrap">
          <img src="/img/login/user.png" alt="" style="width: 1.3rem; height: 1.3rem;">
          <input type="text" :placeholder="$t('auth.username_place')" v-model="username">
        </div>
        <div class="input_wrap">
          <img src="/img/login/pwd.png" alt="" style="width: 1.3rem; height: 1.3rem;">
          <input :placeholder="$t('auth.pwd_place')" v-model="password" :type="pwdType == 1 ? 'password' : 'text'">
          <van-icon :name="pwdType == 0 ? 'eye-o' : 'closed-eye'" color="#FF58D2" size="24" @click="pwdType = pwdType == 1 ? 0 : 1"></van-icon>
        </div>
        <div class="input_wrap">
          <img src="/img/login/code.png" alt="" style="width: 1.00rem; height: 1.00rem;">
          <input type="text" :placeholder="$t('auth.invite_code_place')" v-model="code">
        </div>
      </div>
      <div class="button" @click="doRegister()">{{$t("auth.register")}}</div>
    </div>
  </div>
</template>

<script>
export default {
  model: {
    prop: 'inputValue',
    event: 'input'
  },
  props: {
    /**
     * 当前输入的值
     */
    inputValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pwdType: 1,
      checked: true,
      username: '',
      code: '',
      lang: 'en_us',
      password: this.inputValue,
      passwordType: 'password',
    };
  },
  mounted(){
    
  },
  methods: {
    switchPasswordType() {
      this.passwordType = this.passwordType === 'password' ? 'text' : 'password'
    },
    back(){
      return window.history.back();
    },
    doRegister() {
      if(this.username === "" || this.username === null || this.username === undefined){
        this.$toast.fail(this.$t("auth.username_place"));
        return false;
      }
      if(this.password === "" || this.password === null || this.password === undefined){
        this.$toast.fail(this.$t("auth.pwd_place"));
        return false;
      }
      if(this.code === "" || this.code === null || this.code === undefined){
        this.$toast.fail(this.$t("auth.invite_code_place"));
        return false;
      }
      if(!this.checked){
        this.$toast.fail(this.$t("auth.agreement"));
        return false;
      }
      this.$http({
        method: 'post',
        data:{
          username:this.username,
          password:this.password,
          code:this.code
        },
        url: 'member_register'
      }).then(res=>{
          if(res.code === 200){
            this.$toast.success(res.msg);
            localStorage.setItem('token',res.data)
            this.$router.push("Home")
          }else {
            this.$toast.fail(res.msg);
          }
      })
    }
  },
  created() {
    if(localStorage.getItem('token')){
      return window.history.back();
    }
  }
};
</script>

<style lang='less' scoped>
.van-nav-bar {
  background: none;
}
.bg-container {
  background-image: linear-gradient(180deg,#c7c7c7,#9c9c9c);
  height: 100%;
}
.login {
  height: 100%;
  width: 100%;
  background: url('/public/img/login/register.png') no-repeat 50% / 100%;
  overflow: hidden;
}

.logo {
  width: 13.50rem;
  margin: 5.00rem auto 0.63rem;
  img{
    width: 100%;
  }
}

.wrap{
  .input_wrap{
    width: 90%;
    height: 3.66667rem;
    margin: 1rem auto;
    border-radius: 0.88rem;
    overflow: hidden;
    background: #3E004088;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 0.94rem;
    input{
      flex: 1;
      height: 100%;
      border: none;
      background: none;
      outline: none;
      text-align: left;
      font-size: .93333rem;
      padding-left: 1rem;
      color: #fff;
      &::placeholder {
        color: #ccc;
      }
    }
  }
}

.row {
  font-size: 1rem;
  color: #fff;
  margin: 2.18rem auto;
  text-align: center;
}

.button{
  width: 90%;
  background: url('/public/img/login/button_bg.png') no-repeat 50% / 100%;
  font-size: 1.19rem;
  color: #fff;
  text-align: center;
  height: 4.25rem;
  line-height: 4.25rem;
  margin: 0 auto;
  margin-top: 2.50rem;
}
</style>
