<template>
  <div class="container page main">
    <div class="nav">
      <img src="/img/mine/recharge_arrow_left.png"  @click="back()"/>
      <div class="title">{{ $t('setting.login_pwd_tip') }}</div>
      <div>&nbsp;&nbsp;&nbsp;</div>
    </div>
    <div class="card">
      <div class="header">
        <van-image round class="user_img" src="/img/nan/11.png">
          <template v-slot:loading>
            <van-loading type="spinner"/>
          </template>
        </van-image>
      </div>
      <div class="info">
        <div class="name">{{ userInfo.username }}</div>
        <div class="code">신용점수:{{ userInfo.amount_code }}</div>
      </div>
    </div>

    
    <div class="ipu">
      <div class="row">
        <div class="title">{{ $t("setBank.bank_id") }}</div>
        <div class="value">{{ bankInfo.bankid || '-' }}</div>
      </div>
      <div class="row">
        <div class="title">{{ $t("setBank.user_name") }}</div>
        <div class="value">{{ bankInfo.username || '-' }}</div>
      </div>
      <div class="row">
        <div class="title">{{ $t("setBank.bank_name") }}</div>
        <div class="value">{{ bankInfo.bankinfo || '-' }}</div>
      </div>
      <div class="row">
        <div class="title">{{ $t("setBank.bank_account") }}</div>
        <div class="value">{{ bankInfo.bank_code || '-' }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      is_bind:false,
      bankInfo:{},
      userInfo:{}
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    getUserBankInfo(){
      this.$http({
        method: 'get',
        url: 'user_get_bank'
      }).then(res=>{
        console.log('bankInfo' , res)
        if(res.code === 200){
          this.bankInfo = res.data.info;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        console.log('userInfo' , res)
        if(res.code === 200){
          this.userInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
      this.getUserBankInfo();
    }
  }
};
</script>

<style lang='less' scoped>
.nav{
  width: 95%;
  margin: 5% auto 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  img{
    width: 2.5rem;
    height: 2.8125rem;
  }
  .title{
    color: #ff1a6d;
    font-size: 1.4rem;
  }
}
.card{
  background-image: url("/public/img/mine/bank_card.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // width: 100%;
  height: 10vh;
  margin: 5% 2.5%;
  display: flex;
  align-items: center;
  padding: 2% 5%;
  .info{
    color: #fff;
    margin-left: 1rem;
  }
}


.user_img {
  height: 3.75rem;
  width: 3.75rem;
  border: 1px solid #ff1a6d;
}



.ipu{
  width: 95%;
  margin: 0 auto 0;
  background: linear-gradient(90deg, #332348, #261d3e, #324);
  border-radius: 0.5rem;
  border: 0.125rem solid #5e3876;
  padding: 4%;
  .title{
    font-size: 1rem;
    color: #fff;
  }
  .value{
    font-size: 1rem;
    color: #fff;
    margin-top: 0.6rem;
  }

  .row{
    // border-bottom: 0.125rem solid #5e3876;
    padding: 1rem 0;
  }
  .row + .row{
    border-top: 0.125rem solid #5e3876;
  }
}


</style>
