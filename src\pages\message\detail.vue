<template>
  <div class="container">
    <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('messageDetail.title') }}</span>
      </template>
    </van-nav-bar>
    
    <div class="wrap" id="content">
      <div class="title">{{ messageInfo.title }}</div>
      <div class="label">{{ $t("messageDetail.label") }}:{{ messageInfo.author }}</div>
      <div class="content">{{ messageInfo.content }}</div>
      <div class="time">{{ messageInfo.created_at }}</div>
    </div>
  </div>
</template>

<script>
import message from './message.json'
import { timestempToDate } from "@/common/function"
export default {
  data() {
    return {
      messageInfo: {}
    };
  },
  created() {
    let messageInfo = JSON.parse(localStorage.getItem('messageDetail'))
    // this.messageInfo = message.data.filter(item => item.id == this.$route.query.id)[0]
    this.messageInfo = messageInfo
    console.log(this.messageInfo)
  },
  methods: {
    
    back(){
      return window.history.back();
    },

    getTime(e) {
      return timestempToDate(e)
    },
  }
};
</script>

<style lang='less' scoped>
.van-nav-bar {
  height: 2.88rem;
}

::v-deep .van-tabs {
  .van-tab, .van-tabs__wrap {
    height: 2.75rem;
  }
  .van-tab__text{
    font-size: 0.88rem;
    line-height: 2.75rem;
    color: #6f41b9;
  }
  .van-tab--active .van-tab__text{
    font-weight: bold;
  }
}

.container{
  height: 100%;
  background: #f0f0f0;
}

.wrap{
  height: calc(var(--vh) * 100 - 2.88rem);
  padding: 0.63rem;
  .title{
    padding-top: 0.63rem;
    font-size: 1.00rem;
    color: #000;
    font-weight: bold;
    line-height: 1.88rem;
  }
  .label{
    font-size: 0.75rem;
    padding-bottom: 0.63rem;
    color: #666;
  }
  .content{
    font-size: 1.00rem;
    color: #000;
  }
  .time{
    font-size: 1.00rem;
    color: #888;
    text-align: right;
    padding: 1.25rem 0;
  }
}


</style>
