import Vue from 'vue'
import VueRouter from 'vue-router'
//首页
import Home from '../pages/home/<USER>'/* 首页 */
import XuanfeiDetail from '../pages/home/<USER>'/* 首页 */
import Mine from '../pages/mine/index.vue'/* 我的 */
import Choose from '../pages/choose/index.vue'/* 选妃 */
import List from '../pages/choose/list.vue'/* 选妃列表 */
import Profile from '../pages/choose/profile.vue'/* 选妃详情 */
import Video from '../pages/video/index.vue'/* 视频 */
import Game from '../pages/game/index.vue'/* 游戏 */
import Login from '../pages/login/index.vue'/* 登录 */
import Register from '../pages/login/register.vue'/* 注册 */
import ServiceOnline from '../pages/mine/ServiceOnline.vue'/* 客服列表 */
import ServicePage from '../pages/mine/ServicePage.vue'/* 客服详情界面 */
import Setting from '../pages/mine/Setting.vue'/* 设置 */
import Infomation from '../pages/mine/Infomation.vue'/* 基本信息 */
import Setname from '../pages/mine/Setname.vue'/* 修改姓名 */
import Language from '../pages/mine/Language.vue'/* 语言选择 */
import Setsex from '../pages/mine/Setsex.vue'/* 修改姓名 */
import Recharge from '../pages/mine/Recharge.vue'/* 充值 */
import SetPayPassword from '../pages/mine/SetPayPassword.vue'/* 修改提现密码 */
import SetLoginPassword from '../pages/mine/SetLoginPassword.vue'/* 修改提现密码 */
import Lottery from '../pages/lottery/lottery.vue'/* 彩票详情 */
import Lottery2 from '../pages/lottery/index.vue'/* 彩票详情 */
import Notice from '../pages/mine/Notice.vue'/* 公告 */
import PlayVideo from '../pages/video/PlayVideo'/* 视频播放页面 */
import Setbank from '../pages/mine/Setbank'/* 视频播放页面 */
import BindCard from '../pages/mine/BindCard'/* 绑定银行卡界面 */
import Withdraw from '../pages/mine/Withdraw'/* 绑定银行卡界面 */
import Personalreport from '../pages/mine/Personalreport'/* 个人表报 */
import GameRecord from '../pages/mine/GameRecord'/* 游戏记录 */
import GameRecordDetail from '../pages/mine/GameRecordDetail'/* 游戏记录 */
import Liushui from '../pages/mine/Liushui'/* 流水 */
import WithdrawRecord from '../pages/mine/WithdrawRecord'/* 提现记录 */
import Tx from '../pages/mine/Tx'/* 充值 */
import ChongRecord from '../pages/mine/ChongRecord'/* 充值记录 */
import Goods from '../pages/goods/Goods'/* 充值记录 */
import Info from '../pages/mine/Info'/* 充值记录 */
import WindowLottery from '../pages/lottery/windowLottery'/* 充值记录 */
import Message from '../pages/message/index.vue'/* 充值记录 */
import MessageDetail from '../pages/message/detail.vue'/* 充值记录 */
import Jifen from '../pages/mine/jifen'/* 充值记录 */
import MyMove from '../pages/mine/MyMove'/* 充值记录 */


Vue.use(VueRouter)
const routes = [
  { path: '/', redirect: '/Home', component: Home, meta: { title: '蜜桃俱乐部' } },
  { path: '/Home', name: 'home', component: Home, meta: { title: '蜜桃俱乐部' } },
  { path: '/XuanfeiDetail', name: 'Detail', component: XuanfeiDetail, meta: { title: '蜜桃俱乐部' } },
  { path: '/Choose', name: 'choose', component: Goods, meta: { title: '蜜桃俱乐部' } },
  { path: '/List', name: 'list', component: List, meta: { title: '蜜桃俱乐部' } },
  { path: '/Profile', name: 'profile', component: Profile, meta: { title: '蜜桃俱乐部' } },
  { path: '/Mine', name: 'mine', component: Mine, meta: { title: '蜜桃俱乐部' } },
  { path: '/Video', name: 'video', component: Video, meta: { title: '蜜桃俱乐部' } },
  { path: '/Game', name: 'game', component: Game, meta: { title: '蜜桃俱乐部' } },
  { path: '/Login', name: 'login', component: Login, meta: { title: '蜜桃俱乐部' } },
  { path: '/Register', name: 'register', component: Register, meta: { title: '蜜桃俱乐部' } },
  { path: '/ServiceOnline', name: 'ServiceOnline', component: ServiceOnline, meta: { title: '蜜桃俱乐部' } },
  { path: '/ServicePage', name: 'ServicePage', component: ServicePage, meta: { title: '蜜桃俱乐部' } },
  { path: '/Setting', name: 'Setting', component: Setting, meta: { title: '蜜桃俱乐部' } },
  { path: '/Infomation', name: 'Infomation', component: Infomation, meta: { title: '蜜桃俱乐部' } },
  { path: '/Setname', name: 'Setname', component: Setname, meta: { title: '蜜桃俱乐部' } },
  { path: '/Setsex', name: 'Setsex', component: Setsex, meta: { title: '蜜桃俱乐部' } },
  { path: '/Language', name: 'Language', component: Language, meta: { title: '蜜桃俱乐部' } },
  { path: '/Recharge', name: 'Recharge', component: Recharge, meta: { title: '蜜桃俱乐部' } },
  { path: '/SetPayPassword', name: 'SetPayPassword', component: SetPayPassword, meta: { title: '蜜桃俱乐部' } },
  { path: '/SetLoginPassword', name: 'SetLoginPassword', component: SetLoginPassword, meta: { title: '蜜桃俱乐部' } },
  { path: '/Lottery', name: 'Lottery', component: Lottery, meta: { title: '蜜桃俱乐部' } },
  { path: '/Lottery2', name: 'Lottery2', component: Lottery2, meta: { title: '蜜桃俱乐部' } },
  { path: '/WindowLottery', name: 'WindowLottery', component: WindowLottery, meta: { title: '蜜桃俱乐部' } },
  { path: '/Notice', name: 'Notice', component: Notice, meta: { title: '蜜桃俱乐部' } },
  { path: '/PlayVideo', name: 'PlayVideo', component: PlayVideo, meta: { title: '蜜桃俱乐部' } },
  { path: '/Setbank', name: 'Setbank', component: Setbank, meta: { title: '蜜桃俱乐部' } },
  { path: '/BindCard', name: 'BindCard', component: BindCard, meta: { title: '蜜桃俱乐部' } },
  { path: '/Withdraw', name: 'Withdraw', component: Withdraw, meta: { title: '蜜桃俱乐部' } },
  { path: '/Personalreport', name: 'Personalreport', component: Personalreport, meta: { title: '蜜桃俱乐部' } },
  { path: '/WithdrawRecord', name: 'WithdrawRecord', component: WithdrawRecord, meta: { title: '蜜桃俱乐部' } },
  { path: '/GameRecord', name: 'GameRecord', component: GameRecord, meta: { title: '蜜桃俱乐部' } },
  { path: '/GameRecordDetail', name: 'GameRecordDetail', component: GameRecordDetail, meta: { title: '蜜桃俱乐部' } },
  { path: '/Liushui', name: 'Liushui', component: Liushui, meta: { title: '蜜桃俱乐部' } },
  { path: '/Tx', name: 'Tx', component: Tx, meta: { title: '蜜桃俱乐部' } },
  { path: '/ChongRecord', name: 'ChongRecord', component: ChongRecord, meta: { title: '蜜桃俱乐部' } },
  { path: '/Goods', name: 'Goods', component: Goods, meta: { title: '蜜桃俱乐部' } },
  { path: '/Info', name: 'Info', component: Info, meta: { title: '蜜桃俱乐部' } },
  { path: '/Message', name: 'Message', component: Message, meta: { title: '蜜桃俱乐部' } },
  { path: '/MessageDetail', name: 'MessageDetail', component: MessageDetail, meta: { title: '蜜桃俱乐部' } },
  { path: '/Jifen', name: 'Jifen', component: Jifen, meta: { title: '蜜桃俱乐部' } },
  { path: '/MyMove', name: 'MyMove', component: MyMove, meta: { title: '蜜桃俱乐部' } },


];

//生成路由实例
const router = new VueRouter({
  routes
})
router.beforeEach((to, from, next) => {         //修改标题路由配置加上这个
  document.title = to.matched[0].meta.title
  next()
})

export default router