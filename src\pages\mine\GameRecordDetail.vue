<template>
  <div class="container">
    <van-nav-bar>
      <template #left>
        <van-icon name="arrow-left" size="20" color="#fff" @click="back()" />
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('gameRecordDetail.title') }}</span>
      </template>
    </van-nav-bar>
    
    <div class="wrap" id="content">
      <div class="card1">
        <div class="info">
          <div class="title">{{ gameRecordDetail.lottery_title }}</div>
          <div class="label">{{ $t('gameRecord.label2') }}{{ gameRecordDetail.expect }}{{ $t('gameRecord.label3') }}</div>
        </div>
        <div class="flex">
          <div class="left">
            <div class="title">{{ gameRecordDetail.money }}{{ $t('gameRecordDetail.unit') }}</div>
            <div class="label">{{ $t('gameRecordDetail.label1') }}</div>
          </div>
          <div class="right" :class="{red: loading, green: !loading}">{{ loading ? `${$t('gameRecordDetail.status1')}` : `${$t('gameRecordDetail.status')}` }}</div>
        </div>
      </div>
      <div class="card2">
        <div>
          <span class="label">{{ $t('gameRecordDetail.label2') }}: </span>
          <span class="value">{{ gameRecordDetail.tzcode }}</span>
        </div>
        <div>
          <span class="label">{{ $t('gameRecordDetail.label3') }}: </span>
          <span class="value">{{ gameRecordDetail.create_time }}</span>
        </div>
      </div>
      <div class="card3">
        <div class="title">{{ $t('gameRecordDetail.label4') }}</div>
        <table border="0" cellpadding="0" cellspacing="0">
          <tr>
            <td>{{ $t('gameRecordDetail.column1') }}</td>
            <td>{{ $t('gameRecordDetail.column2') }}</td>
            <td>{{ $t('gameRecordDetail.column3') }}</td>
            <td>{{ $t('gameRecordDetail.column4') }}</td>
          </tr>
          <tr v-for="item in gameRecordList" :key="item.id">
            <td>{{ item.type_name }}</td>
            <td>{{ item.money }}</td>
            <td :class="{ red: item.is_win !== 1, green: item.is_win === 1 }">{{ item.win }}</td>
            <td>{{ item.create_time }}</td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      gameRecordList: [],
      gameRecordDetail: {},
      loading: false
    };
  },
  created() {
    this.getGameRecordDetail()
  },
  methods: {
    getGameRecordDetail(){
      this.gameRecordDetail = JSON.parse(localStorage.getItem('recordDetail'))
      console.log(localStorage.getItem('recordDetail'))
      const params = {
        lid: this.gameRecordDetail.lid,
        expect: this.gameRecordDetail.expect
      }
      return this.$http({
        method: 'get',
        url: 'game_record_detail',
        data: params
      }).then(res=>{
        this.gameRecordList = res.data.map(item => {
          item.win = item.is_win === 0 ? this.$t('gameRecordDetail.status2') : item.is_win === 1 ? this.$t('gameRecordDetail.status3') : this.$t('gameRecordDetail.status1')
          return item
        })
      })
    },
    back(){
      return window.history.back();
    },

  }
};
</script>

<style lang='less' scoped>
.card1{
  margin: 0 1.38rem;
  .info{
    padding: 1.13rem 0;
    .title{
      font-size: 1.25rem;
      font-weight: bold;
      text-align: center;
    }
    .label{
      font-size: 0.88rem;
      color: #7d7d7d;
      padding-top: 0.38rem;
      text-align: center;
    }
  }
  .flex {
    height: 7.50rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left, .right {
      width: 50%;
      text-align: center;
    }
    .title {
      font-size: 1.25rem;
      font-weight: bold;
      text-align: center;
    }
    .label {
      padding-top: 0.75rem;
      color: #999;
      font-size: 0.75rem;
    }
    .right {
      font-size: 1.25rem;
      font-weight: bold;
      border-left: 0.06rem solid #f2f2f2;
    }
    .green {
      color: green;
    }
    .red {
      color: #f56969;
    }
  }
}
.card2 {
  height: 7.50rem;
  padding: 1.38rem;
  border-top: 0.31rem solid #f2f2f5;
  border-bottom: 0.31rem solid #f2f2f5;
  >div {
    height: 2.38rem;
    padding: 0.56rem 0;
  }
  .label {
    font-size: 0.88rem;
  }
  .value {
    font-size: 1.00rem;
  }
}
.card3 {
  padding: 1.38rem;
  .title {
    font-size: 1.13rem;
    padding-bottom: 1.50rem;
    font-weight: bold
  }
  table {
    width: 100%;
    td{
      text-align: center;
      padding: 0.50rem 0;
      border: 0.06rem solid #eee;
      font-size: 0.88rem;
    }
    .green{
      color: #36c989;
    }
    .red{
      color: #f56969;
    }
  }
}
</style>
